'use client';

import { useState, useEffect } from 'react';

export function AccessibilityFeatures() {
  const [isHighContrast, setIsHighContrast] = useState(false);
  const [fontSize, setFontSize] = useState('normal');
  const [isReducedMotion, setIsReducedMotion] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check for saved preferences
    const savedHighContrast = localStorage.getItem('highContrast') === 'true';
    const savedFontSize = localStorage.getItem('fontSize') || 'normal';
    const savedReducedMotion = localStorage.getItem('reducedMotion') === 'true';

    setIsHighContrast(savedHighContrast);
    setFontSize(savedFontSize);
    setIsReducedMotion(savedReducedMotion);

    // Apply saved preferences
    applyHighContrast(savedHighContrast);
    applyFontSize(savedFontSize);
    applyReducedMotion(savedReducedMotion);

    // Check for system preferences
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion && !savedReducedMotion) {
      setIsReducedMotion(true);
      applyReducedMotion(true);
    }
  }, []);

  const applyHighContrast = (enabled: boolean) => {
    if (enabled) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
    localStorage.setItem('highContrast', enabled.toString());
  };

  const applyFontSize = (size: string) => {
    document.documentElement.classList.remove('font-small', 'font-normal', 'font-large', 'font-xl');
    document.documentElement.classList.add(`font-${size}`);
    localStorage.setItem('fontSize', size);
  };

  const applyReducedMotion = (enabled: boolean) => {
    if (enabled) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }
    localStorage.setItem('reducedMotion', enabled.toString());
  };

  const toggleHighContrast = () => {
    const newValue = !isHighContrast;
    setIsHighContrast(newValue);
    applyHighContrast(newValue);
  };

  const changeFontSize = (size: string) => {
    setFontSize(size);
    applyFontSize(size);
  };

  const toggleReducedMotion = () => {
    const newValue = !isReducedMotion;
    setIsReducedMotion(newValue);
    applyReducedMotion(newValue);
  };

  const resetToDefaults = () => {
    setIsHighContrast(false);
    setFontSize('normal');
    setIsReducedMotion(false);
    applyHighContrast(false);
    applyFontSize('normal');
    applyReducedMotion(false);
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="Open accessibility options"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-6 w-80">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Accessibility Options</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
          aria-label="Close accessibility options"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="space-y-4">
        {/* High Contrast */}
        <div className="flex items-center justify-between">
          <label htmlFor="high-contrast" className="text-sm font-medium text-gray-700">
            High Contrast
          </label>
          <button
            id="high-contrast"
            onClick={toggleHighContrast}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              isHighContrast ? 'bg-blue-600' : 'bg-gray-200'
            }`}
            role="switch"
            aria-checked={isHighContrast}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                isHighContrast ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Font Size */}
        <div>
          <label className="text-sm font-medium text-gray-700 block mb-2">
            Font Size
          </label>
          <div className="grid grid-cols-4 gap-2">
            {['small', 'normal', 'large', 'xl'].map((size) => (
              <button
                key={size}
                onClick={() => changeFontSize(size)}
                className={`px-3 py-2 text-xs rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  fontSize === size
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {size === 'xl' ? 'XL' : size.charAt(0).toUpperCase() + size.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Reduced Motion */}
        <div className="flex items-center justify-between">
          <label htmlFor="reduced-motion" className="text-sm font-medium text-gray-700">
            Reduce Motion
          </label>
          <button
            id="reduced-motion"
            onClick={toggleReducedMotion}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              isReducedMotion ? 'bg-blue-600' : 'bg-gray-200'
            }`}
            role="switch"
            aria-checked={isReducedMotion}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                isReducedMotion ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {/* Reset Button */}
        <button
          onClick={resetToDefaults}
          className="w-full mt-4 px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        >
          Reset to Defaults
        </button>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          Need help? Contact our{' '}
          <a href="/contact" className="text-blue-600 hover:text-blue-700 underline">
            support team
          </a>
        </p>
      </div>
    </div>
  );
}

// Add corresponding CSS classes to global.css
export const accessibilityStyles = `
/* High contrast mode */
.high-contrast {
  --color-primary: #000000;
  --color-secondary: #ffffff;
  --text-primary: #000000;
  --text-secondary: #ffffff;
  --bg-primary: #ffffff;
  --bg-secondary: #000000;
}

.high-contrast .card,
.high-contrast .card-hover,
.high-contrast .card-interactive {
  border: 2px solid #000000 !important;
  background: #ffffff !important;
}

.high-contrast .btn-primary {
  background: #000000 !important;
  color: #ffffff !important;
  border: 2px solid #000000 !important;
}

.high-contrast .btn-outline {
  background: #ffffff !important;
  color: #000000 !important;
  border: 2px solid #000000 !important;
}

/* Font size variations */
.font-small {
  font-size: 14px;
}

.font-normal {
  font-size: 16px;
}

.font-large {
  font-size: 18px;
}

.font-xl {
  font-size: 20px;
}

/* Reduced motion */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}
`;
