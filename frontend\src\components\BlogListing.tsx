'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { BlogSearch } from './BlogSearch';
import Link from 'next/link';

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  category: string;
  readTime: string;
  publishedAt: string;
  slug: string;
  image?: string;
  tags?: string[];
}

const mockPosts: BlogPost[] = [
  {
    id: 1,
    title: 'WordPress Security Best Practices for 2024',
    excerpt: 'Learn the essential security measures to protect your WordPress site from threats and vulnerabilities.',
    category: 'Security',
    readTime: '8 min read',
    publishedAt: '2024-01-15',
    slug: 'wordpress-security-best-practices',
    tags: ['WordPress', 'Security', 'Best Practices']
  },
  {
    id: 2,
    title: 'Shopify Conversion Optimization: Boost Your Sales by 40%',
    excerpt: 'Discover proven strategies to optimize your Shopify store for higher conversions and increased revenue.',
    category: 'Analytics',
    readTime: '6 min read',
    publishedAt: '2024-01-10',
    slug: 'shopify-conversion-optimization',
    tags: ['Shopify', 'Conversion', 'E-commerce']
  },
  {
    id: 3,
    title: 'Browser Extension Development: Complete Guide',
    excerpt: 'A comprehensive guide to developing browser extensions for Chrome, Firefox, and other browsers.',
    category: 'Tutorial',
    readTime: '12 min read',
    publishedAt: '2024-01-05',
    slug: 'browser-extension-development-guide',
    tags: ['Browser Extensions', 'Development', 'Tutorial']
  },
  {
    id: 4,
    title: 'Advanced Analytics for E-commerce Success',
    excerpt: 'Unlock the power of data analytics to drive your e-commerce business growth and customer insights.',
    category: 'Analytics',
    readTime: '10 min read',
    publishedAt: '2024-01-01',
    slug: 'advanced-analytics-ecommerce',
    tags: ['Analytics', 'E-commerce', 'Data']
  },
  {
    id: 5,
    title: 'WordPress Performance Optimization Tips',
    excerpt: 'Speed up your WordPress site with these proven performance optimization techniques and tools.',
    category: 'Performance',
    readTime: '7 min read',
    publishedAt: '2023-12-28',
    slug: 'wordpress-performance-optimization',
    tags: ['WordPress', 'Performance', 'Speed']
  },
  {
    id: 6,
    title: 'Shopify App Integration Best Practices',
    excerpt: 'Learn how to properly integrate third-party apps with your Shopify store for maximum efficiency.',
    category: 'Integration',
    readTime: '9 min read',
    publishedAt: '2023-12-25',
    slug: 'shopify-app-integration-best-practices',
    tags: ['Shopify', 'Integration', 'Apps']
  }
];

export function BlogListing() {
  const t = useTranslations('Blog');
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>(mockPosts);

  const handleFilteredPosts = (posts: BlogPost[]) => {
    setFilteredPosts(posts);
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'Security': 'bg-red-100 text-red-800',
      'Analytics': 'bg-purple-100 text-purple-800',
      'Tutorial': 'bg-blue-100 text-blue-800',
      'Performance': 'bg-green-100 text-green-800',
      'Integration': 'bg-yellow-100 text-yellow-800',
      'Tips': 'bg-indigo-100 text-indigo-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Search and Filters */}
      <BlogSearch posts={mockPosts} onFilteredPosts={handleFilteredPosts} />

      {/* Results Count */}
      <div className="mb-8">
        <p className="text-gray-600">
          Showing {filteredPosts.length} of {mockPosts.length} articles
        </p>
      </div>

      {/* Blog Posts Grid */}
      {filteredPosts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPosts.map((post) => (
            <article key={post.id} className="card-hover group">
              <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-lg flex items-center justify-center">
                <span className="text-gray-600 font-medium">Article Image</span>
              </div>
              <div className="p-6">
                <div className="flex items-center mb-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium mr-2 ${getCategoryColor(post.category)}`}>
                    {post.category}
                  </span>
                  <span className="text-gray-500 text-xs">{post.readTime}</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                  <Link href={`/blog/${post.slug}`}>
                    {post.title}
                  </Link>
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <time className="text-sm text-gray-500">
                    {new Date(post.publishedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </time>
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors"
                  >
                    {t('read_more')}
                  </Link>
                </div>
                {post.tags && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="flex flex-wrap gap-1">
                      {post.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded"
                        >
                          {tag}
                        </span>
                      ))}
                      {post.tags.length > 3 && (
                        <span className="text-xs text-gray-500">
                          +{post.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </article>
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No articles found</h3>
          <p className="text-gray-600 mb-6">
            Try adjusting your search criteria or browse all categories.
          </p>
          <button 
            onClick={() => {
              setFilteredPosts(mockPosts);
              // This would trigger a reset in the search component
            }}
            className="btn-outline"
          >
            Show All Articles
          </button>
        </div>
      )}

      {/* Load More Button */}
      {filteredPosts.length > 0 && filteredPosts.length >= 6 && (
        <div className="text-center mt-12">
          <button className="btn-outline">
            Load More Articles
          </button>
        </div>
      )}
    </div>
  );
}
