{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28marketing%29/products/browser/page.tsx"], "sourcesContent": ["import { getTranslations, setRequestLocale } from 'next-intl/server';\nimport { ProductDemo } from '@/components/ProductDemo';\n\ntype IBrowserProductsProps = {\n  params: Promise<{ locale: string }>;\n};\n\nexport async function generateMetadata(props: IBrowserProductsProps) {\n  const { locale } = await props.params;\n  const t = await getTranslations({\n    locale,\n    namespace: 'BrowserProducts',\n  });\n\n  return {\n    title: t('meta_title'),\n    description: t('meta_description'),\n  };\n}\n\nexport default async function BrowserProducts(props: IBrowserProductsProps) {\n  const { locale } = await props.params;\n  setRequestLocale(locale);\n  const t = await getTranslations({\n    locale,\n    namespace: 'BrowserProducts',\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 to-white\">\n      {/* Hero Section */}\n      <section className=\"relative py-16 lg:py-24 overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\n          <div className=\"text-center max-w-4xl mx-auto\">\n            <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-purple-100 text-purple-800 text-sm font-medium mb-6\">\n              <svg className=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\" />\n              </svg>\n              {t('hero_badge')}\n            </div>\n            <h1 className=\"text-hero text-gradient mb-6 animate-fade-in-up\">\n              {t('hero_title')}\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 animate-fade-in-up\" style={{ animationDelay: '0.1s' }}>\n              {t('hero_description')}\n            </p>\n\n            {/* Hero Stats */}\n            <div className=\"flex flex-col sm:flex-row gap-8 justify-center mb-12 animate-fade-in-up\" style={{ animationDelay: '0.15s' }}>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-purple-600\">{t('hero_stats_users')}</div>\n                <div className=\"text-sm text-gray-600\">{t('hero_stats_users_label')}</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600\">{t('hero_stats_time_saved')}</div>\n                <div className=\"text-sm text-gray-600\">{t('hero_stats_time_saved_label')}</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600\">{t('hero_stats_browsers')}</div>\n                <div className=\"text-sm text-gray-600\">{t('hero_stats_browsers_label')}</div>\n              </div>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up\" style={{ animationDelay: '0.2s' }}>\n              <button className=\"btn-gradient text-lg px-8 py-4\">\n                {t('install_free')}\n              </button>\n              <button className=\"btn-outline text-lg px-8 py-4\">\n                {t('view_demo')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Product Showcase */}\n      <section className=\"py-16 lg:py-24 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-section text-gray-900 mb-6\">\n              {t('products_title')}\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              {t('products_description')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* Productivity Suite */}\n            <div className=\"card-hover p-8 group\">\n              <div className=\"flex items-center mb-6\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-purple-600 transition-all duration-300\">\n                  <svg className=\"w-6 h-6 text-purple-600 group-hover:text-white transition-colors duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"text-2xl font-semibold text-gray-900\">{t('productivity_suite_title')}</h3>\n                  <div className=\"text-sm text-purple-600 font-medium\">{t('productivity_suite_badge')}</div>\n                </div>\n              </div>\n              <p className=\"text-gray-600 mb-6\">{t('productivity_suite_description')}</p>\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center text-sm\">\n                  <svg className=\"w-4 h-4 text-green-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>{t('productivity_suite_feature_1')}</span>\n                </div>\n                <div className=\"flex items-center text-sm\">\n                  <svg className=\"w-4 h-4 text-green-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>{t('productivity_suite_feature_2')}</span>\n                </div>\n                <div className=\"flex items-center text-sm\">\n                  <svg className=\"w-4 h-4 text-green-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>{t('productivity_suite_feature_3')}</span>\n                </div>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-2xl font-bold text-gray-900\">{t('productivity_suite_price')}</div>\n                <button className=\"btn-gradient px-6 py-2\">{t('install_now')}</button>\n              </div>\n            </div>\n\n            {/* Shopping Assistant */}\n            <div className=\"card-hover p-8 group\">\n              <div className=\"flex items-center mb-6\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-blue-600 transition-all duration-300\">\n                  <svg className=\"w-6 h-6 text-blue-600 group-hover:text-white transition-colors duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"text-2xl font-semibold text-gray-900\">{t('shopping_assistant_title')}</h3>\n                  <div className=\"text-sm text-blue-600 font-medium\">{t('shopping_assistant_badge')}</div>\n                </div>\n              </div>\n              <p className=\"text-gray-600 mb-6\">{t('shopping_assistant_description')}</p>\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center text-sm\">\n                  <svg className=\"w-4 h-4 text-green-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>{t('shopping_assistant_feature_1')}</span>\n                </div>\n                <div className=\"flex items-center text-sm\">\n                  <svg className=\"w-4 h-4 text-green-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>{t('shopping_assistant_feature_2')}</span>\n                </div>\n                <div className=\"flex items-center text-sm\">\n                  <svg className=\"w-4 h-4 text-green-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span>{t('shopping_assistant_feature_3')}</span>\n                </div>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-2xl font-bold text-gray-900\">{t('shopping_assistant_price')}</div>\n                <button className=\"btn-gradient px-6 py-2\">{t('install_now')}</button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Browser Compatibility */}\n      <section className=\"py-16 lg:py-24 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-section text-gray-900 mb-6\">\n              {t('compatibility_title')}\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              {t('compatibility_description')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm\">\n                <span className=\"text-2xl font-bold text-blue-600\">C</span>\n              </div>\n              <div className=\"text-sm font-medium text-gray-900\">{t('browser_chrome')}</div>\n              <div className=\"text-xs text-gray-600\">{t('browser_chrome_version')}</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm\">\n                <span className=\"text-2xl font-bold text-orange-600\">F</span>\n              </div>\n              <div className=\"text-sm font-medium text-gray-900\">{t('browser_firefox')}</div>\n              <div className=\"text-xs text-gray-600\">{t('browser_firefox_version')}</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm\">\n                <span className=\"text-2xl font-bold text-blue-500\">E</span>\n              </div>\n              <div className=\"text-sm font-medium text-gray-900\">{t('browser_edge')}</div>\n              <div className=\"text-xs text-gray-600\">{t('browser_edge_version')}</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm\">\n                <span className=\"text-2xl font-bold text-gray-600\">S</span>\n              </div>\n              <div className=\"text-sm font-medium text-gray-900\">{t('browser_safari')}</div>\n              <div className=\"text-xs text-gray-600\">{t('browser_safari_version')}</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Privacy & Security */}\n      <section className=\"py-16 lg:py-24 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-section text-gray-900 mb-6\">\n              {t('privacy_title')}\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              {t('privacy_description')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('privacy_feature_1_title')}</h3>\n              <p className=\"text-gray-600\">{t('privacy_feature_1_description')}</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('privacy_feature_2_title')}</h3>\n              <p className=\"text-gray-600\">{t('privacy_feature_2_description')}</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('privacy_feature_3_title')}</h3>\n              <p className=\"text-gray-600\">{t('privacy_feature_3_description')}</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\" style={{ backgroundColor: 'var(--color-primary)' }}>\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-section text-white mb-6\">\n            {t('cta_title')}\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            {t('cta_description')}\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-6\">\n            <button className=\"bg-white hover:bg-gray-50 font-medium px-8 py-4 rounded-lg transition-colors duration-200\" style={{ color: 'var(--color-primary)' }}>\n              {t('install_extensions')}\n            </button>\n            <button className=\"border-2 border-white text-white hover:bg-white font-medium px-8 py-4 rounded-lg transition-all duration-200\">\n              {t('learn_more')}\n            </button>\n          </div>\n          <p className=\"text-sm text-blue-200\">\n            ✓ {t('free_forever_guarantee')}\n          </p>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;;AAOO,eAAe,iBAAiB,KAA4B;IACjE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;AAEe,eAAe,gBAAgB,KAA4B;IACxE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IACjB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;oCAET,EAAE;;;;;;;0CAEL,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;gCAAgD,OAAO;oCAAE,gBAAgB;gCAAO;0CAC1F,EAAE;;;;;;0CAIL,8OAAC;gCAAI,WAAU;gCAA0E,OAAO;oCAAE,gBAAgB;gCAAQ;;kDACxH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC,EAAE;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAyB,EAAE;;;;;;;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAoC,EAAE;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DAAyB,EAAE;;;;;;;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAqC,EAAE;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DAAyB,EAAE;;;;;;;;;;;;;;;;;;0CAI9C,8OAAC;gCAAI,WAAU;gCAA0E,OAAO;oCAAE,gBAAgB;gCAAO;;kDACvH,8OAAC;wCAAO,WAAU;kDACf,EAAE;;;;;;kDAEL,8OAAC;wCAAO,WAAU;kDACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAgF,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACvI,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC,EAAE;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAAuC,EAAE;;;;;;;;;;;;;;;;;;sDAG5D,8OAAC;4CAAE,WAAU;sDAAsB,EAAE;;;;;;sDACrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA8B,MAAK;4DAAe,SAAQ;sEACvE,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;sEAE3J,8OAAC;sEAAM,EAAE;;;;;;;;;;;;8DAEX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA8B,MAAK;4DAAe,SAAQ;sEACvE,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;sEAE3J,8OAAC;sEAAM,EAAE;;;;;;;;;;;;8DAEX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA8B,MAAK;4DAAe,SAAQ;sEACvE,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;sEAE3J,8OAAC;sEAAM,EAAE;;;;;;;;;;;;;;;;;;sDAGb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC,EAAE;;;;;;8DACrD,8OAAC;oDAAO,WAAU;8DAA0B,EAAE;;;;;;;;;;;;;;;;;;8CAKlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA8E,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACrI,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC,EAAE;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAAqC,EAAE;;;;;;;;;;;;;;;;;;sDAG1D,8OAAC;4CAAE,WAAU;sDAAsB,EAAE;;;;;;sDACrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA8B,MAAK;4DAAe,SAAQ;sEACvE,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;sEAE3J,8OAAC;sEAAM,EAAE;;;;;;;;;;;;8DAEX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA8B,MAAK;4DAAe,SAAQ;sEACvE,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;sEAE3J,8OAAC;sEAAM,EAAE;;;;;;;;;;;;8DAEX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAA8B,MAAK;4DAAe,SAAQ;sEACvE,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;sEAE3J,8OAAC;sEAAM,EAAE;;;;;;;;;;;;;;;;;;sDAGb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC,EAAE;;;;;;8DACrD,8OAAC;oDAAO,WAAU;8DAA0B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;;;;;;sDAErD,8OAAC;4CAAI,WAAU;sDAAqC,EAAE;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAyB,EAAE;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;sDAAqC,EAAE;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAyB,EAAE;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;;;;;;sDAErD,8OAAC;4CAAI,WAAU;sDAAqC,EAAE;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAyB,EAAE;;;;;;;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;;;;;;sDAErD,8OAAC;4CAAI,WAAU;sDAAqC,EAAE;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA4C,EAAE;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;sDAAiB,EAAE;;;;;;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA4C,EAAE;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;sDAAiB,EAAE;;;;;;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA4C,EAAE;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;sDAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,8OAAC;gBAAQ,WAAU;gBAA6B,OAAO;oBAAE,iBAAiB;gBAAuB;0BAC/F,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;oCAA4F,OAAO;wCAAE,OAAO;oCAAuB;8CAClJ,EAAE;;;;;;8CAEL,8OAAC;oCAAO,WAAU;8CACf,EAAE;;;;;;;;;;;;sCAGP,8OAAC;4BAAE,WAAU;;gCAAwB;gCAChC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMjB", "debugId": null}}]}