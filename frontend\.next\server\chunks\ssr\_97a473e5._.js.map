{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/PricingCalculator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PricingCalculator = registerClientReference(\n    function() { throw new Error(\"Attempted to call PricingCalculator() from the server but PricingCalculator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PricingCalculator.tsx <module evaluation>\",\n    \"PricingCalculator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,sEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/PricingCalculator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PricingCalculator = registerClientReference(\n    function() { throw new Error(\"Attempted to call PricingCalculator() from the server but PricingCalculator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/PricingCalculator.tsx\",\n    \"PricingCalculator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,kDACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28marketing%29/pricing/page.tsx"], "sourcesContent": ["import { getTranslations, setRequestLocale } from 'next-intl/server';\nimport { PricingCalculator } from '@/components/PricingCalculator';\n\ntype IPricingProps = {\n  params: Promise<{ locale: string }>;\n};\n\nexport async function generateMetadata(props: IPricingProps) {\n  const { locale } = await props.params;\n  const t = await getTranslations({\n    locale,\n    namespace: 'Pricing',\n  });\n\n  return {\n    title: t('meta_title'),\n    description: t('meta_description'),\n  };\n}\n\nexport default async function Pricing(props: IPricingProps) {\n  const { locale } = await props.params;\n  setRequestLocale(locale);\n  const t = await getTranslations({\n    locale,\n    namespace: 'Pricing',\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-white\">\n      {/* Hero Section */}\n      <section className=\"relative py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center\">\n            <h1 className=\"text-hero text-gradient mb-6\">\n              {t('hero_title')}\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-12\">\n              {t('hero_description')}\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Pricing Calculator */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <PricingCalculator />\n      </section>\n\n      {/* Static Pricing Cards for Reference */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-section text-gray-900 mb-6\">\n              Standard Pricing Plans\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Our standard pricing without customization\n            </p>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {/* Free Plan */}\n            <div className=\"card-hover p-8 relative\">\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  {t('free_plan_title')}\n                </h3>\n                <div className=\"mb-6\">\n                  <span className=\"text-4xl font-bold text-gray-900\">$0</span>\n                  <span className=\"text-gray-600 ml-2\">{t('per_month')}</span>\n                </div>\n                <p className=\"text-gray-600 mb-8\">\n                  {t('free_plan_description')}\n                </p>\n                <button className=\"btn-outline w-full mb-8\">\n                  {t('get_started_free')}\n                </button>\n                <ul className=\"space-y-4 text-left\">\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('free_feature_1')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('free_feature_2')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('free_feature_3')}\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            {/* Pro Plan */}\n            <div className=\"card-hover p-8 relative border-2 border-blue-500\">\n              <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                <span className=\"bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium\">\n                  {t('most_popular')}\n                </span>\n              </div>\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  {t('pro_plan_title')}\n                </h3>\n                <div className=\"mb-6\">\n                  <span className=\"text-4xl font-bold text-gray-900\">$29</span>\n                  <span className=\"text-gray-600 ml-2\">{t('per_month')}</span>\n                </div>\n                <p className=\"text-gray-600 mb-8\">\n                  {t('pro_plan_description')}\n                </p>\n                <button className=\"btn-primary w-full mb-8\">\n                  {t('start_free_trial')}\n                </button>\n                <ul className=\"space-y-4 text-left\">\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('pro_feature_1')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('pro_feature_2')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('pro_feature_3')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('pro_feature_4')}\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            {/* Enterprise Plan */}\n            <div className=\"card-hover p-8 relative\">\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  {t('enterprise_plan_title')}\n                </h3>\n                <div className=\"mb-6\">\n                  <span className=\"text-4xl font-bold text-gray-900\">$99</span>\n                  <span className=\"text-gray-600 ml-2\">{t('per_month')}</span>\n                </div>\n                <p className=\"text-gray-600 mb-8\">\n                  {t('enterprise_plan_description')}\n                </p>\n                <button className=\"btn-secondary w-full mb-8\">\n                  {t('contact_sales')}\n                </button>\n                <ul className=\"space-y-4 text-left\">\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('enterprise_feature_1')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('enterprise_feature_2')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('enterprise_feature_3')}\n                  </li>\n                  <li className=\"flex items-center\">\n                    <svg className=\"w-5 h-5 text-green-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                    {t('enterprise_feature_4')}\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-section text-gray-900 mb-6\">\n              {t('faq_title')}\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              {t('faq_description')}\n            </p>\n          </div>\n          <div className=\"space-y-8\">\n            <div className=\"card p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                {t('faq_1_question')}\n              </h3>\n              <p className=\"text-gray-600\">\n                {t('faq_1_answer')}\n              </p>\n            </div>\n            <div className=\"card p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                {t('faq_2_question')}\n              </h3>\n              <p className=\"text-gray-600\">\n                {t('faq_2_answer')}\n              </p>\n            </div>\n            <div className=\"card p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                {t('faq_3_question')}\n              </h3>\n              <p className=\"text-gray-600\">\n                {t('faq_3_answer')}\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;;AAMO,eAAe,iBAAiB,KAAoB;IACzD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;AAEe,eAAe,QAAQ,KAAoB;IACxD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IACjB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC,uIAAA,CAAA,oBAAiB;;;;;;;;;;0BAIpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAGhD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,EAAE;;;;;;0DAEL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAK,WAAU;kEAAsB,EAAE;;;;;;;;;;;;0DAE1C,8OAAC;gDAAE,WAAU;0DACV,EAAE;;;;;;0DAEL,8OAAC;gDAAO,WAAU;0DACf,EAAE;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J,EAAE;;;;;;;kEAEL,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J,EAAE;;;;;;;kEAEL,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J,EAAE;;;;;;;;;;;;;;;;;;;;;;;;8CAOX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,EAAE;;;;;;;;;;;sDAGP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,EAAE;;;;;;8DAEL,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,8OAAC;4DAAK,WAAU;sEAAsB,EAAE;;;;;;;;;;;;8DAE1C,8OAAC;oDAAE,WAAU;8DACV,EAAE;;;;;;8DAEL,8OAAC;oDAAO,WAAU;8DACf,EAAE;;;;;;8DAEL,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAe,SAAQ;8EACvE,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;gEAE1J,EAAE;;;;;;;sEAEL,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAe,SAAQ;8EACvE,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;gEAE1J,EAAE;;;;;;;sEAEL,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAe,SAAQ;8EACvE,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;gEAE1J,EAAE;;;;;;;sEAEL,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAe,SAAQ;8EACvE,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAqH,UAAS;;;;;;;;;;;gEAE1J,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;8CAOX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,EAAE;;;;;;0DAEL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAK,WAAU;kEAAsB,EAAE;;;;;;;;;;;;0DAE1C,8OAAC;gDAAE,WAAU;0DACV,EAAE;;;;;;0DAEL,8OAAC;gDAAO,WAAU;0DACf,EAAE;;;;;;0DAEL,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J,EAAE;;;;;;;kEAEL,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J,EAAE;;;;;;;kEAEL,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J,EAAE;;;;;;;kEAEL,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAqH,UAAS;;;;;;;;;;;4DAE1J,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAGP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;8CAGP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;8CAGP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG,cAAc;oBAAA,GAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;wCACVC,IAAAA;oCAAAA,CAAMZ,UAAUa;oCAAAA,OAAQ;yCACxBC,MAAM;8CACNC,IAAAA,CAAAA,GAAU;wCAAA,QAAA;4CAAA,IAAA;4CAAA;yCAAA;;uCACV,2CAA2C;;iCAC3CC,YAAY;sCACZC,IAAAA,CAAAA;4BAAAA,CAAU;yBAAA;;yBACVC,UAAU,EAAE;0BACd,QAAA,CAAA;wBAAA,UAAA;4BAAA,IAAA;4BAAA;yBAAA;;mBACAC,UAAU;;iBACRC,YAAYnB;kBACd,QAAA,CAAA;gBAAA,UAAA;oBAAA,IAAA;oBAAA;iBAAA;YACF;SAAA,CAAE", "ignoreList": [0], "debugId": null}}]}