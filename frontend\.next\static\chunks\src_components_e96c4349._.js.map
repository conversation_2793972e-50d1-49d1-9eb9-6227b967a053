{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/NewsletterSignup.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations } from 'next-intl';\n\nexport function NewsletterSignup() {\n  const t = useTranslations('Blog');\n  const [email, setEmail] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubscribed, setIsSubscribed] = useState(false);\n  const [error, setError] = useState('');\n\n  const validateEmail = (email: string): boolean => {\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!email.trim()) {\n      setError('Email is required');\n      return;\n    }\n\n    if (!validateEmail(email)) {\n      setError('Please enter a valid email address');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setError('');\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      // Here you would typically send the email to your backend\n      console.log('Newsletter signup:', email);\n      \n      setIsSubscribed(true);\n      setEmail('');\n    } catch (error) {\n      console.error('Error subscribing to newsletter:', error);\n      setError('Something went wrong. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setEmail(e.target.value);\n    if (error) {\n      setError('');\n    }\n  };\n\n  if (isSubscribed) {\n    return (\n      <div className=\"max-w-4xl mx-auto text-center\">\n        <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n          </svg>\n        </div>\n        <h2 className=\"text-section text-gray-900 mb-6\">\n          Thank You for Subscribing!\n        </h2>\n        <p className=\"text-xl text-gray-600 mb-8\">\n          You'll receive our latest insights and updates directly in your inbox.\n        </p>\n        <button \n          onClick={() => setIsSubscribed(false)}\n          className=\"btn-outline\"\n        >\n          Subscribe Another Email\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto text-center\">\n      <h2 className=\"text-section text-gray-900 mb-6\">\n        {t('newsletter_title')}\n      </h2>\n      <p className=\"text-xl text-gray-600 mb-8\">\n        {t('newsletter_description')}\n      </p>\n      <form onSubmit={handleSubmit} className=\"max-w-md mx-auto\">\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <div className=\"flex-1\">\n            <input\n              type=\"email\"\n              value={email}\n              onChange={handleEmailChange}\n              placeholder={t('email_placeholder')}\n              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${\n                error ? 'border-red-500' : 'border-gray-300'\n              }`}\n              disabled={isSubmitting}\n            />\n            {error && (\n              <p className=\"mt-2 text-sm text-red-600 text-left\">{error}</p>\n            )}\n          </div>\n          <button \n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"btn-primary whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n          >\n            {isSubmitting ? (\n              <>\n                <div className=\"spinner w-5 h-5 mr-2\"></div>\n                Subscribing...\n              </>\n            ) : (\n              t('subscribe')\n            )}\n          </button>\n        </div>\n      </form>\n      <p className=\"text-sm text-gray-500 mt-4\">\n        {t('newsletter_disclaimer')}\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB,CAAC;QACrB,OAAO,6BAA6B,IAAI,CAAC;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,cAAc,QAAQ;YACzB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0DAA0D;YAC1D,QAAQ,GAAG,CAAC,sBAAsB;YAElC,gBAAgB;YAChB,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS,EAAE,MAAM,CAAC,KAAK;QACvB,IAAI,OAAO;YACT,SAAS;QACX;IACF;IAEA,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAyB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAChF,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAG,WAAU;8BAAkC;;;;;;8BAGhD,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBACC,SAAS,IAAM,gBAAgB;oBAC/B,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BACX,EAAE;;;;;;0BAEL,6LAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;0BAEL,6LAAC;gBAAK,UAAU;gBAAc,WAAU;0BACtC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU;oCACV,aAAa,EAAE;oCACf,WAAW,CAAC,+GAA+G,EACzH,QAAQ,mBAAmB,mBAC3B;oCACF,UAAU;;;;;;gCAEX,uBACC,6LAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAGxD,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAA6B;;+CAI9C,EAAE;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;;;;;;;AAIX;GAzHgB;;QACJ,yMAAA,CAAA,kBAAe;;;KADX", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/BlogSearch.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useTranslations } from 'next-intl';\n\ninterface BlogPost {\n  id: number;\n  title: string;\n  excerpt: string;\n  category: string;\n  readTime: string;\n  publishedAt: string;\n  image?: string;\n  tags?: string[];\n}\n\ninterface BlogSearchProps {\n  posts: BlogPost[];\n  onFilteredPosts: (posts: BlogPost[]) => void;\n}\n\nconst categories = [\n  { id: 'all', label: 'All Categories' },\n  { id: 'tutorial', label: 'Tutorial' },\n  { id: 'analytics', label: 'Analytics' },\n  { id: 'integration', label: 'Integration' },\n  { id: 'tips', label: 'Tips & Tricks' },\n  { id: 'security', label: 'Security' },\n  { id: 'performance', label: 'Performance' }\n];\n\nexport function BlogSearch({ posts, onFilteredPosts }: BlogSearchProps) {\n  const t = useTranslations('Blog');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest');\n\n  useEffect(() => {\n    let filteredPosts = [...posts];\n\n    // Filter by search query\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase();\n      filteredPosts = filteredPosts.filter(post =>\n        post.title.toLowerCase().includes(query) ||\n        post.excerpt.toLowerCase().includes(query) ||\n        post.category.toLowerCase().includes(query) ||\n        post.tags?.some(tag => tag.toLowerCase().includes(query))\n      );\n    }\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      filteredPosts = filteredPosts.filter(post =>\n        post.category.toLowerCase() === selectedCategory.toLowerCase()\n      );\n    }\n\n    // Sort posts\n    filteredPosts.sort((a, b) => {\n      switch (sortBy) {\n        case 'newest':\n          return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();\n        case 'oldest':\n          return new Date(a.publishedAt).getTime() - new Date(b.publishedAt).getTime();\n        case 'popular':\n          // For now, sort by title as a placeholder for popularity\n          return a.title.localeCompare(b.title);\n        default:\n          return 0;\n      }\n    });\n\n    onFilteredPosts(filteredPosts);\n  }, [searchQuery, selectedCategory, sortBy, posts, onFilteredPosts]);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchQuery(e.target.value);\n  };\n\n  const handleCategoryChange = (category: string) => {\n    setSelectedCategory(category);\n  };\n\n  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    setSortBy(e.target.value as 'newest' | 'oldest' | 'popular');\n  };\n\n  const clearFilters = () => {\n    setSearchQuery('');\n    setSelectedCategory('all');\n    setSortBy('newest');\n  };\n\n  const hasActiveFilters = searchQuery.trim() || selectedCategory !== 'all' || sortBy !== 'newest';\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n      <div className=\"flex flex-col lg:flex-row gap-6\">\n        {/* Search Input */}\n        <div className=\"flex-1\">\n          <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Search Articles\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n            <input\n              type=\"text\"\n              id=\"search\"\n              value={searchQuery}\n              onChange={handleSearchChange}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Search by title, content, or tags...\"\n            />\n          </div>\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"lg:w-48\">\n          <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Category\n          </label>\n          <select\n            id=\"category\"\n            value={selectedCategory}\n            onChange={(e) => handleCategoryChange(e.target.value)}\n            className=\"block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            {categories.map((category) => (\n              <option key={category.id} value={category.id}>\n                {category.label}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* Sort Options */}\n        <div className=\"lg:w-48\">\n          <label htmlFor=\"sort\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Sort By\n          </label>\n          <select\n            id=\"sort\"\n            value={sortBy}\n            onChange={handleSortChange}\n            className=\"block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"newest\">Newest First</option>\n            <option value=\"oldest\">Oldest First</option>\n            <option value=\"popular\">Most Popular</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Active Filters & Clear */}\n      {hasActiveFilters && (\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex flex-wrap gap-2\">\n              {searchQuery.trim() && (\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800\">\n                  Search: \"{searchQuery}\"\n                  <button\n                    onClick={() => setSearchQuery('')}\n                    className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                  >\n                    ×\n                  </button>\n                </span>\n              )}\n              {selectedCategory !== 'all' && (\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800\">\n                  Category: {categories.find(c => c.id === selectedCategory)?.label}\n                  <button\n                    onClick={() => setSelectedCategory('all')}\n                    className=\"ml-2 text-purple-600 hover:text-purple-800\"\n                  >\n                    ×\n                  </button>\n                </span>\n              )}\n              {sortBy !== 'newest' && (\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800\">\n                  Sort: {sortBy === 'oldest' ? 'Oldest First' : 'Most Popular'}\n                  <button\n                    onClick={() => setSortBy('newest')}\n                    className=\"ml-2 text-green-600 hover:text-green-800\"\n                  >\n                    ×\n                  </button>\n                </span>\n              )}\n            </div>\n            <button\n              onClick={clearFilters}\n              className=\"text-sm text-gray-600 hover:text-gray-800 font-medium\"\n            >\n              Clear All Filters\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAqBA,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,OAAO;IAAiB;IACrC;QAAE,IAAI;QAAY,OAAO;IAAW;IACpC;QAAE,IAAI;QAAa,OAAO;IAAY;IACtC;QAAE,IAAI;QAAe,OAAO;IAAc;IAC1C;QAAE,IAAI;QAAQ,OAAO;IAAgB;IACrC;QAAE,IAAI;QAAY,OAAO;IAAW;IACpC;QAAE,IAAI;QAAe,OAAO;IAAc;CAC3C;AAEM,SAAS,WAAW,EAAE,KAAK,EAAE,eAAe,EAAmB;;IACpE,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,gBAAgB;mBAAI;aAAM;YAE9B,yBAAyB;YACzB,IAAI,YAAY,IAAI,IAAI;gBACtB,MAAM,QAAQ,YAAY,WAAW;gBACrC,gBAAgB,cAAc,MAAM;4CAAC,CAAA,OACnC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UAClC,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,UACpC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,KAAK,IAAI,EAAE;oDAAK,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;;;YAEtD;YAEA,qBAAqB;YACrB,IAAI,qBAAqB,OAAO;gBAC9B,gBAAgB,cAAc,MAAM;4CAAC,CAAA,OACnC,KAAK,QAAQ,CAAC,WAAW,OAAO,iBAAiB,WAAW;;YAEhE;YAEA,aAAa;YACb,cAAc,IAAI;wCAAC,CAAC,GAAG;oBACrB,OAAQ;wBACN,KAAK;4BACH,OAAO,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;wBAC5E,KAAK;4BACH,OAAO,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;wBAC5E,KAAK;4BACH,yDAAyD;4BACzD,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;wBACtC;4BACE,OAAO;oBACX;gBACF;;YAEA,gBAAgB;QAClB;+BAAG;QAAC;QAAa;QAAkB;QAAQ;QAAO;KAAgB;IAElE,MAAM,qBAAqB,CAAC;QAC1B,eAAe,EAAE,MAAM,CAAC,KAAK;IAC/B;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACxB,UAAU,EAAE,MAAM,CAAC,KAAK;IAC1B;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,oBAAoB;QACpB,UAAU;IACZ;IAEA,MAAM,mBAAmB,YAAY,IAAI,MAAM,qBAAqB,SAAS,WAAW;IAExF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAA+C;;;;;;0CAGjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU;wCACV,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,WAAU;0CAET,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wCAAyB,OAAO,SAAS,EAAE;kDACzC,SAAS,KAAK;uCADJ,SAAS,EAAE;;;;;;;;;;;;;;;;kCAQ9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU;gCACV,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;YAM7B,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,YAAY,IAAI,oBACf,6LAAC;oCAAK,WAAU;;wCAAoF;wCACxF;wCAAY;sDACtB,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;;;;;;gCAKJ,qBAAqB,uBACpB,6LAAC;oCAAK,WAAU;;wCAAwF;wCAC3F,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB;sDAC5D,6LAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDACX;;;;;;;;;;;;gCAKJ,WAAW,0BACV,6LAAC;oCAAK,WAAU;;wCAAsF;wCAC7F,WAAW,WAAW,iBAAiB;sDAC9C,6LAAC;4CACC,SAAS,IAAM,UAAU;4CACzB,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAMP,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjLgB;;QACJ,yMAAA,CAAA,kBAAe;;;KADX", "debugId": null}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/BlogListing.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations } from 'next-intl';\nimport { BlogSearch } from './BlogSearch';\nimport Link from 'next/link';\n\ninterface BlogPost {\n  id: number;\n  title: string;\n  excerpt: string;\n  category: string;\n  readTime: string;\n  publishedAt: string;\n  slug: string;\n  image?: string;\n  tags?: string[];\n}\n\nconst mockPosts: BlogPost[] = [\n  {\n    id: 1,\n    title: 'WordPress Security Best Practices for 2024',\n    excerpt: 'Learn the essential security measures to protect your WordPress site from threats and vulnerabilities.',\n    category: 'Security',\n    readTime: '8 min read',\n    publishedAt: '2024-01-15',\n    slug: 'wordpress-security-best-practices',\n    tags: ['WordPress', 'Security', 'Best Practices']\n  },\n  {\n    id: 2,\n    title: 'Shopify Conversion Optimization: Boost Your Sales by 40%',\n    excerpt: 'Discover proven strategies to optimize your Shopify store for higher conversions and increased revenue.',\n    category: 'Analytics',\n    readTime: '6 min read',\n    publishedAt: '2024-01-10',\n    slug: 'shopify-conversion-optimization',\n    tags: ['Shopify', 'Conversion', 'E-commerce']\n  },\n  {\n    id: 3,\n    title: 'Browser Extension Development: Complete Guide',\n    excerpt: 'A comprehensive guide to developing browser extensions for Chrome, Firefox, and other browsers.',\n    category: 'Tutorial',\n    readTime: '12 min read',\n    publishedAt: '2024-01-05',\n    slug: 'browser-extension-development-guide',\n    tags: ['Browser Extensions', 'Development', 'Tutorial']\n  },\n  {\n    id: 4,\n    title: 'Advanced Analytics for E-commerce Success',\n    excerpt: 'Unlock the power of data analytics to drive your e-commerce business growth and customer insights.',\n    category: 'Analytics',\n    readTime: '10 min read',\n    publishedAt: '2024-01-01',\n    slug: 'advanced-analytics-ecommerce',\n    tags: ['Analytics', 'E-commerce', 'Data']\n  },\n  {\n    id: 5,\n    title: 'WordPress Performance Optimization Tips',\n    excerpt: 'Speed up your WordPress site with these proven performance optimization techniques and tools.',\n    category: 'Performance',\n    readTime: '7 min read',\n    publishedAt: '2023-12-28',\n    slug: 'wordpress-performance-optimization',\n    tags: ['WordPress', 'Performance', 'Speed']\n  },\n  {\n    id: 6,\n    title: 'Shopify App Integration Best Practices',\n    excerpt: 'Learn how to properly integrate third-party apps with your Shopify store for maximum efficiency.',\n    category: 'Integration',\n    readTime: '9 min read',\n    publishedAt: '2023-12-25',\n    slug: 'shopify-app-integration-best-practices',\n    tags: ['Shopify', 'Integration', 'Apps']\n  }\n];\n\nexport function BlogListing() {\n  const t = useTranslations('Blog');\n  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>(mockPosts);\n\n  const handleFilteredPosts = (posts: BlogPost[]) => {\n    setFilteredPosts(posts);\n  };\n\n  const getCategoryColor = (category: string) => {\n    const colors = {\n      'Security': 'bg-red-100 text-red-800',\n      'Analytics': 'bg-purple-100 text-purple-800',\n      'Tutorial': 'bg-blue-100 text-blue-800',\n      'Performance': 'bg-green-100 text-green-800',\n      'Integration': 'bg-yellow-100 text-yellow-800',\n      'Tips': 'bg-indigo-100 text-indigo-800'\n    };\n    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n      {/* Search and Filters */}\n      <BlogSearch posts={mockPosts} onFilteredPosts={handleFilteredPosts} />\n\n      {/* Results Count */}\n      <div className=\"mb-8\">\n        <p className=\"text-gray-600\">\n          Showing {filteredPosts.length} of {mockPosts.length} articles\n        </p>\n      </div>\n\n      {/* Blog Posts Grid */}\n      {filteredPosts.length > 0 ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredPosts.map((post) => (\n            <article key={post.id} className=\"card-hover group\">\n              <div className=\"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-lg flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium\">Article Image</span>\n              </div>\n              <div className=\"p-6\">\n                <div className=\"flex items-center mb-3\">\n                  <span className={`px-2 py-1 rounded text-xs font-medium mr-2 ${getCategoryColor(post.category)}`}>\n                    {post.category}\n                  </span>\n                  <span className=\"text-gray-500 text-xs\">{post.readTime}</span>\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\n                  <Link href={`/blog/${post.slug}`}>\n                    {post.title}\n                  </Link>\n                </h3>\n                <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                  {post.excerpt}\n                </p>\n                <div className=\"flex items-center justify-between\">\n                  <time className=\"text-sm text-gray-500\">\n                    {new Date(post.publishedAt).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'short',\n                      day: 'numeric'\n                    })}\n                  </time>\n                  <Link \n                    href={`/blog/${post.slug}`}\n                    className=\"text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors\"\n                  >\n                    {t('read_more')}\n                  </Link>\n                </div>\n                {post.tags && (\n                  <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                    <div className=\"flex flex-wrap gap-1\">\n                      {post.tags.slice(0, 3).map((tag, index) => (\n                        <span\n                          key={index}\n                          className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\"\n                        >\n                          {tag}\n                        </span>\n                      ))}\n                      {post.tags.length > 3 && (\n                        <span className=\"text-xs text-gray-500\">\n                          +{post.tags.length - 3} more\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </article>\n          ))}\n        </div>\n      ) : (\n        <div className=\"text-center py-16\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No articles found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            Try adjusting your search criteria or browse all categories.\n          </p>\n          <button \n            onClick={() => {\n              setFilteredPosts(mockPosts);\n              // This would trigger a reset in the search component\n            }}\n            className=\"btn-outline\"\n          >\n            Show All Articles\n          </button>\n        </div>\n      )}\n\n      {/* Load More Button */}\n      {filteredPosts.length > 0 && filteredPosts.length >= 6 && (\n        <div className=\"text-center mt-12\">\n          <button className=\"btn-outline\">\n            Load More Articles\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAmBA,MAAM,YAAwB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;YAAC;YAAa;YAAY;SAAiB;IACnD;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAc;SAAa;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;YAAC;YAAsB;YAAe;SAAW;IACzD;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;YAAC;YAAa;YAAc;SAAO;IAC3C;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;YAAC;YAAa;YAAe;SAAQ;IAC7C;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAe;SAAO;IAC1C;CACD;AAEM,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAE/D,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,eAAe;YACf,eAAe;YACf,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI;IACpD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,aAAU;gBAAC,OAAO;gBAAW,iBAAiB;;;;;;0BAG/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAgB;wBAClB,cAAc,MAAM;wBAAC;wBAAK,UAAU,MAAM;wBAAC;;;;;;;;;;;;YAKvD,cAAc,MAAM,GAAG,kBACtB,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;wBAAsB,WAAU;;0CAC/B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;0CAE9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,iBAAiB,KAAK,QAAQ,GAAG;0DAC7F,KAAK,QAAQ;;;;;;0DAEhB,6LAAC;gDAAK,WAAU;0DAAyB,KAAK,QAAQ;;;;;;;;;;;;kDAExD,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;sDAC7B,KAAK,KAAK;;;;;;;;;;;kDAGf,6LAAC;wCAAE,WAAU;kDACV,KAAK,OAAO;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,SAAS;oDACtD,MAAM;oDACN,OAAO;oDACP,KAAK;gDACP;;;;;;0DAEF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;gDAC1B,WAAU;0DAET,EAAE;;;;;;;;;;;;oCAGN,KAAK,IAAI,kBACR,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;gDAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;oDAAK,WAAU;;wDAAwB;wDACpC,KAAK,IAAI,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;uBA/CvB,KAAK,EAAE;;;;;;;;;qCA0DzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBACC,SAAS;4BACP,iBAAiB;wBACjB,qDAAqD;wBACvD;wBACA,WAAU;kCACX;;;;;;;;;;;;YAOJ,cAAc,MAAM,GAAG,KAAK,cAAc,MAAM,IAAI,mBACnD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAO,WAAU;8BAAc;;;;;;;;;;;;;;;;;AAO1C;GA9HgB;;QACJ,yMAAA,CAAA,kBAAe;;;KADX", "debugId": null}}]}