{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/utils/AppConfig.ts"], "sourcesContent": ["import type { LocalizationResource } from '@clerk/types';\r\nimport type { LocalePrefixMode } from 'next-intl/routing';\r\nimport { enUS, frFR } from '@clerk/localizations';\r\n\r\nconst localePrefix: LocalePrefixMode = 'as-needed';\r\n\r\n// AppExtera configuration\r\nexport const AppConfig = {\r\n  name: 'AppExtera',\r\n  locales: ['en', 'fr'],\r\n  defaultLocale: 'en',\r\n  localePrefix,\r\n};\r\n\r\nconst supportedLocales: Record<string, LocalizationResource> = {\r\n  en: enUS,\r\n  fr: frFR,\r\n};\r\n\r\nexport const ClerkLocalizations = {\r\n  defaultLocale: enUS,\r\n  supportedLocales,\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;AAEA,MAAM,eAAiC;AAGhC,MAAM,YAAY;IACvB,MAAM;IACN,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf;AACF;AAEA,MAAM,mBAAyD;IAC7D,IAAI,0JAAA,CAAA,OAAI;IACR,IAAI,0JAAA,CAAA,OAAI;AACV;AAEO,MAAM,qBAAqB;IAChC,eAAe,0JAAA,CAAA,OAAI;IACnB;AACF", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nRouting.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\r\nimport { AppConfig } from '@/utils/AppConfig';\r\n\r\nexport const routing = defineRouting({\r\n  locales: AppConfig.locales,\r\n  localePrefix: AppConfig.localePrefix,\r\n  defaultLocale: AppConfig.defaultLocale,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,SAAS,yHAAA,CAAA,YAAS,CAAC,OAAO;IAC1B,cAAc,yHAAA,CAAA,YAAS,CAAC,YAAY;IACpC,eAAe,yHAAA,CAAA,YAAS,CAAC,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18n.ts"], "sourcesContent": ["import { hasLocale } from 'next-intl';\r\nimport { getRequestConfig } from 'next-intl/server';\r\nimport { routing } from './I18nRouting';\r\n\r\n// NextJS Boilerplate uses Crowdin as the localization software.\r\n// As a developer, you only need to take care of the English (or another default language) version.\r\n// Other languages are automatically generated and handled by <PERSON><PERSON>.\r\n\r\n// The localisation files are synced with Crowdin using GitHub Actions.\r\n// By default, there are 3 ways to sync the message files:\r\n// 1. Automatically sync on push to the `main` branch\r\n// 2. Run manually the workflow on GitHub Actions\r\n// 3. Every 24 hours at 5am, the workflow will run automatically\r\n\r\nexport default getRequestConfig(async ({ requestLocale }) => {\r\n  // Typically corresponds to the `[locale]` segment\r\n  const requested = await requestLocale;\r\n  const locale = hasLocale(routing.locales, requested)\r\n    ? requested\r\n    : routing.defaultLocale;\r\n\r\n  return {\r\n    locale,\r\n    messages: (await import(`../locales/${locale}.json`)).default,\r\n  };\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCAYe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,aAAa,EAAE;IACtD,kDAAkD;IAClD,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,0HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,aACtC,YACA,0HAAA,CAAA,UAAO,CAAC,aAAa;IAEzB,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/analytics/PostHogProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/PostHogProvider.tsx <module evaluation>\",\n    \"PostHogProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/analytics/PostHogProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/analytics/PostHogProvider.tsx\",\n    \"PostHogProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next';\r\nimport { hasLocale, NextIntlClientProvider } from 'next-intl';\r\nimport { setRequestLocale } from 'next-intl/server';\r\nimport { notFound } from 'next/navigation';\r\nimport { PostHogProvider } from '@/components/analytics/PostHogProvider';\r\nimport { routing } from '@/libs/I18nRouting';\r\nimport '@/styles/global.css';\r\n\r\nexport const metadata: Metadata = {\r\n  icons: [\r\n    {\r\n      rel: 'apple-touch-icon',\r\n      url: '/apple-touch-icon.png',\r\n    },\r\n    {\r\n      rel: 'icon',\r\n      type: 'image/png',\r\n      sizes: '32x32',\r\n      url: '/favicon-32x32.png',\r\n    },\r\n    {\r\n      rel: 'icon',\r\n      type: 'image/png',\r\n      sizes: '16x16',\r\n      url: '/favicon-16x16.png',\r\n    },\r\n    {\r\n      rel: 'icon',\r\n      url: '/favicon.ico',\r\n    },\r\n  ],\r\n};\r\n\r\nexport function generateStaticParams() {\r\n  return routing.locales.map(locale => ({ locale }));\r\n}\r\n\r\nexport default async function RootLayout(props: {\r\n  children: React.ReactNode;\r\n  params: Promise<{ locale: string }>;\r\n}) {\r\n  const { locale } = await props.params;\r\n\r\n  if (!hasLocale(routing.locales, locale)) {\r\n    notFound();\r\n  }\r\n\r\n  setRequestLocale(locale);\r\n\r\n  return (\r\n    <html lang={locale}>\r\n      <body suppressHydrationWarning={true}>\r\n        <NextIntlClientProvider>\r\n          <PostHogProvider>\r\n            {props.children}\r\n          </PostHogProvider>\r\n        </NextIntlClientProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;QACL;YACE,KAAK;YACL,KAAK;QACP;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;YACP,KAAK;QACP;QACA;YACE,KAAK;YACL,MAAM;YACN,OAAO;YACP,KAAK;QACP;QACA;YACE,KAAK;YACL,KAAK;QACP;KACD;AACH;AAEO,SAAS;IACd,OAAO,0HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;YAAE;QAAO,CAAC;AAClD;AAEe,eAAe,WAAW,KAGxC;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IAErC,IAAI,CAAC,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,0HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,SAAS;QACvC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IAEjB,qBACE,8OAAC;QAAK,MAAM;kBACV,cAAA,8OAAC;YAAK,0BAA0B;sBAC9B,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;0BACrB,cAAA,8OAAC,kJAAA,CAAA,kBAAe;8BACb,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}]}