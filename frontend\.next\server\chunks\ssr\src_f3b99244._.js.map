{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/utils/AppConfig.ts"], "sourcesContent": ["import type { LocalizationResource } from '@clerk/types';\r\nimport type { LocalePrefixMode } from 'next-intl/routing';\r\nimport { enUS, frFR } from '@clerk/localizations';\r\n\r\nconst localePrefix: LocalePrefixMode = 'as-needed';\r\n\r\n// AppExtera configuration\r\nexport const AppConfig = {\r\n  name: 'AppExtera',\r\n  locales: ['en', 'fr'],\r\n  defaultLocale: 'en',\r\n  localePrefix,\r\n};\r\n\r\nconst supportedLocales: Record<string, LocalizationResource> = {\r\n  en: enUS,\r\n  fr: frFR,\r\n};\r\n\r\nexport const ClerkLocalizations = {\r\n  defaultLocale: enUS,\r\n  supportedLocales,\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;AAEA,MAAM,eAAiC;AAGhC,MAAM,YAAY;IACvB,MAAM;IACN,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf;AACF;AAEA,MAAM,mBAAyD;IAC7D,IAAI,0JAAA,CAAA,OAAI;IACR,IAAI,0JAAA,CAAA,OAAI;AACV;AAEO,MAAM,qBAAqB;IAChC,eAAe,0JAAA,CAAA,OAAI;IACnB;AACF", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nRouting.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\r\nimport { AppConfig } from '@/utils/AppConfig';\r\n\r\nexport const routing = defineRouting({\r\n  locales: AppConfig.locales,\r\n  localePrefix: AppConfig.localePrefix,\r\n  defaultLocale: AppConfig.defaultLocale,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,SAAS,yHAAA,CAAA,YAAS,CAAC,OAAO;IAC1B,cAAc,yHAAA,CAAA,YAAS,CAAC,YAAY;IACpC,eAAe,yHAAA,CAAA,YAAS,CAAC,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/libs/I18nNavigation.ts"], "sourcesContent": ["import { createNavigation } from 'next-intl/navigation';\r\nimport { routing } from './I18nRouting';\r\n\r\nexport const { usePathname } = createNavigation(routing);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE,0HAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/LocaleSwitcher.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ChangeEventHandler } from 'react';\r\nimport { useLocale } from 'next-intl';\r\nimport { useRouter } from 'next/navigation';\r\nimport { usePathname } from '@/libs/I18nNavigation';\r\nimport { routing } from '@/libs/I18nRouting';\r\n\r\nexport const LocaleSwitcher = () => {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const locale = useLocale();\r\n\r\n  const handleChange: ChangeEventHandler<HTMLSelectElement> = (event) => {\r\n    router.push(`/${event.target.value}${pathname}`);\r\n    router.refresh(); // Ensure the page takes the new locale into account related to the issue #395\r\n  };\r\n\r\n  return (\r\n    <select\r\n      defaultValue={locale}\r\n      onChange={handleChange}\r\n      className=\"border border-gray-300 font-medium focus:outline-hidden focus-visible:ring-3\"\r\n      aria-label=\"lang-switcher\"\r\n    >\r\n      {routing.locales.map(elt => (\r\n        <option key={elt} value={elt}>\r\n          {elt.toUpperCase()}\r\n        </option>\r\n      ))}\r\n    </select>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQO,MAAM,iBAAiB;IAC5B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAsD,CAAC;QAC3D,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,KAAK,GAAG,UAAU;QAC/C,OAAO,OAAO,IAAI,8EAA8E;IAClG;IAEA,qBACE,8OAAC;QACC,cAAc;QACd,UAAU;QACV,WAAU;QACV,cAAW;kBAEV,0HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,oBACnB,8OAAC;gBAAiB,OAAO;0BACtB,IAAI,WAAW;eADL;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations } from 'next-intl';\nimport Link from 'next/link';\nimport { LocaleSwitcher } from '@/components/LocaleSwitcher';\nimport { AppConfig } from '@/utils/AppConfig';\n\nexport const Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isProductsOpen, setIsProductsOpen] = useState(false);\n  const t = useTranslations('RootLayout');\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n  const toggleProducts = () => setIsProductsOpen(!isProductsOpen);\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50 shadow-sm\">\n      <div className=\"container-wide\">\n        <div className=\"flex items-center justify-between h-20\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\">\n                <span className=\"text-white font-bold text-xl\">A</span>\n              </div>\n              <span className=\"text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300\">\n                {AppConfig.name}\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:block\">\n            <div className=\"ml-10 flex items-center space-x-8\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n              >\n                {t('home_link')}\n              </Link>\n\n              {/* Products Dropdown */}\n              <div className=\"relative\">\n                <button\n                  onClick={toggleProducts}\n                  className=\"text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 flex items-center rounded-lg hover:bg-blue-50\"\n                >\n                  {t('products_link')}\n                  <svg className={`ml-2 h-4 w-4 transition-transform duration-300 ${isProductsOpen ? 'rotate-180' : ''}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n                {isProductsOpen && (\n                  <div className=\"absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-gray-100 py-4 z-50 animate-fade-in-up\">\n                    <Link\n                      href=\"/products/wordpress/\"\n                      className=\"block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200\"\n                      onClick={() => setIsProductsOpen(false)}\n                    >\n                      <div className=\"flex items-center\">\n                        <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\">\n                          <span className=\"text-blue-600 text-xs font-bold\">WP</span>\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">{t('products_wordpress')}</div>\n                          <div className=\"text-xs text-gray-500\">{t('products_wordpress_desc')}</div>\n                        </div>\n                      </div>\n                    </Link>\n                    <Link\n                      href=\"/products/shopify/\"\n                      className=\"block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200\"\n                      onClick={() => setIsProductsOpen(false)}\n                    >\n                      <div className=\"flex items-center\">\n                        <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3\">\n                          <span className=\"text-green-600 text-xs font-bold\">SH</span>\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">{t('products_shopify')}</div>\n                          <div className=\"text-xs text-gray-500\">{t('products_shopify_desc')}</div>\n                        </div>\n                      </div>\n                    </Link>\n                    <Link\n                      href=\"/products/browser/\"\n                      className=\"block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200\"\n                      onClick={() => setIsProductsOpen(false)}\n                    >\n                      <div className=\"flex items-center\">\n                        <div className=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3\">\n                          <span className=\"text-purple-600 text-xs font-bold\">BR</span>\n                        </div>\n                        <div>\n                          <div className=\"font-medium\">{t('products_browser')}</div>\n                          <div className=\"text-xs text-gray-500\">{t('products_browser_desc')}</div>\n                        </div>\n                      </div>\n                    </Link>\n                  </div>\n                )}\n              </div>\n\n              <Link\n                href=\"/features/\"\n                className=\"text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n              >\n                {t('features_link')}\n              </Link>\n              <Link\n                href=\"/about/\"\n                className=\"text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n              >\n                {t('about_link')}\n              </Link>\n              <Link\n                href=\"/blog/\"\n                className=\"text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n              >\n                {t('blog_link')}\n              </Link>\n            </div>\n          </div>\n\n          {/* Desktop Right Side */}\n          <div className=\"hidden lg:flex items-center space-x-6\">\n            <LocaleSwitcher />\n            <Link\n              href=\"/contact/\"\n              className=\"text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n            >\n              {t('contact_link')}\n            </Link>\n            <Link\n              href=\"/contact/\"\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 text-sm font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\n            >\n              Get Started\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"lg:hidden\">\n            <button\n              onClick={toggleMenu}\n              className=\"inline-flex items-center justify-center p-3 rounded-xl text-gray-700 hover:text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {!isMenuOpen ? (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              ) : (\n                <svg className=\"block h-6 w-6\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"px-4 pt-4 pb-6 space-y-2 bg-white border-t border-gray-200 shadow-lg\">\n              <Link\n                href=\"/\"\n                className=\"text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('home_link')}\n              </Link>\n              <Link\n                href=\"/features/\"\n                className=\"text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('features_link')}\n              </Link>\n              <Link\n                href=\"/about/\"\n                className=\"text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('about_link')}\n              </Link>\n              <Link\n                href=\"/blog/\"\n                className=\"text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('blog_link')}\n              </Link>\n              <div className=\"border-t border-gray-200 pt-6 mt-4\">\n                <div className=\"flex items-center px-3 space-x-3 mb-4\">\n                  <LocaleSwitcher />\n                </div>\n                <Link\n                  href=\"/contact/\"\n                  className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white block mx-3 py-3 text-center font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Get Started\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,MAAM,aAAa;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,iBAAiB,IAAM,kBAAkB,CAAC;IAEhD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDACb,yHAAA,CAAA,YAAS,CAAC,IAAI;;;;;;;;;;;;;;;;;sCAMrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAIL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;;oDAET,EAAE;kEACH,8OAAC;wDAAI,WAAW,CAAC,+CAA+C,EAAE,iBAAiB,eAAe,IAAI;wDAAE,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAChJ,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAGxE,gCACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEjC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAkC;;;;;;;;;;;8EAEpD,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAe,EAAE;;;;;;sFAChC,8OAAC;4EAAI,WAAU;sFAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;kEAIhD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEjC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;8EAErD,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAe,EAAE;;;;;;sFAChC,8OAAC;4EAAI,WAAU;sFAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;kEAIhD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEjC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAoC;;;;;;;;;;;8EAEtD,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAe,EAAE;;;;;;sFAChC,8OAAC;4EAAI,WAAU;sFAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,iBAAc;;;;;8CACf,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,CAAC,2BACA,8OAAC;wCAAI,WAAU;wCAAgB,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;wCAAe,eAAY;kDAClI,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;6DAGvE,8OAAC;wCAAI,WAAU;wCAAgB,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;wCAAe,eAAY;kDAClI,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;kDAEjB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}