'use client';

import { useState } from 'react';

interface DemoStep {
  id: string;
  title: string;
  description: string;
  image?: string;
  features: string[];
}

interface ProductDemoProps {
  productType: 'wordpress' | 'shopify' | 'browser';
}

const demoSteps = {
  wordpress: [
    {
      id: 'security-scan',
      title: 'Security Scan',
      description: 'AI-powered threat detection scans your entire WordPress site for vulnerabilities and malware.',
      features: [
        'Real-time malware detection',
        'Vulnerability assessment',
        'File integrity monitoring',
        'Suspicious activity alerts'
      ]
    },
    {
      id: 'performance-optimization',
      title: 'Performance Optimization',
      description: 'Automatic performance optimization with intelligent caching and code minification.',
      features: [
        'Smart caching system',
        'Image optimization',
        'Code minification',
        'Database cleanup'
      ]
    },
    {
      id: 'seo-analysis',
      title: 'SEO Analysis',
      description: 'Comprehensive SEO analysis with actionable recommendations for better rankings.',
      features: [
        'Content optimization',
        'Meta tag analysis',
        'Schema markup',
        'Competitor insights'
      ]
    }
  ],
  shopify: [
    {
      id: 'subscription-management',
      title: 'Subscription Management',
      description: 'Advanced subscription management with flexible billing and customer retention tools.',
      features: [
        'Flexible billing cycles',
        'Churn prediction',
        'Customer portal',
        'Automated renewals'
      ]
    },
    {
      id: 'upsell-engine',
      title: 'Upsell Engine',
      description: 'AI-powered product recommendations that increase average order value.',
      features: [
        'Smart recommendations',
        'Post-purchase upsells',
        'Bundle suggestions',
        'A/B testing'
      ]
    },
    {
      id: 'analytics-dashboard',
      title: 'Analytics Dashboard',
      description: 'Comprehensive analytics dashboard with revenue insights and performance metrics.',
      features: [
        'Revenue tracking',
        'Customer insights',
        'Performance metrics',
        'Custom reports'
      ]
    }
  ],
  browser: [
    {
      id: 'tab-management',
      title: 'Tab Management',
      description: 'Intelligent tab organization and management for improved productivity.',
      features: [
        'Auto-grouping tabs',
        'Tab search',
        'Session saving',
        'Memory optimization'
      ]
    },
    {
      id: 'shopping-assistant',
      title: 'Shopping Assistant',
      description: 'Automatic coupon discovery and price tracking for online shopping.',
      features: [
        'Coupon discovery',
        'Price tracking',
        'Deal alerts',
        'Cashback rewards'
      ]
    },
    {
      id: 'productivity-tools',
      title: 'Productivity Tools',
      description: 'Suite of productivity tools to enhance your browsing experience.',
      features: [
        'Website blocking',
        'Focus mode',
        'Quick notes',
        'Bookmark sync'
      ]
    }
  ]
};

export function ProductDemo({ productType }: ProductDemoProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const steps = demoSteps[productType];

  const handlePlayDemo = () => {
    setIsPlaying(true);
    setCurrentStep(0);
    
    // Auto-advance through steps
    const interval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev >= steps.length - 1) {
          setIsPlaying(false);
          clearInterval(interval);
          return 0;
        }
        return prev + 1;
      });
    }, 3000);
  };

  const handleStepClick = (index: number) => {
    setCurrentStep(index);
    setIsPlaying(false);
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
      {/* Demo Header */}
      <div className="bg-gray-900 text-white p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold mb-2">Interactive Demo</h3>
            <p className="text-gray-300">See how our {productType} solution works</p>
          </div>
          <button
            onClick={handlePlayDemo}
            disabled={isPlaying}
            className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
          >
            {isPlaying ? (
              <>
                <div className="spinner w-5 h-5 mr-2"></div>
                Playing...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
                Play Demo
              </>
            )}
          </button>
        </div>
      </div>

      {/* Demo Content */}
      <div className="p-6">
        {/* Step Navigation */}
        <div className="flex space-x-2 mb-6 overflow-x-auto">
          {steps.map((step, index) => (
            <button
              key={step.id}
              onClick={() => handleStepClick(index)}
              className={`flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                index === currentStep
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {index + 1}. {step.title}
            </button>
          ))}
        </div>

        {/* Current Step Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Demo Visualization */}
          <div className="relative">
            <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <p className="text-gray-600 font-medium">{steps[currentStep].title} Demo</p>
                <p className="text-sm text-gray-500 mt-2">Interactive visualization</p>
              </div>
            </div>
            
            {/* Progress Indicator */}
            {isPlaying && (
              <div className="absolute bottom-4 left-4 right-4">
                <div className="bg-black bg-opacity-50 rounded-full h-1">
                  <div 
                    className="bg-blue-500 h-1 rounded-full transition-all duration-3000 ease-linear"
                    style={{ width: isPlaying ? '100%' : '0%' }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          {/* Step Details */}
          <div>
            <h4 className="text-2xl font-bold text-gray-900 mb-4">
              {steps[currentStep].title}
            </h4>
            <p className="text-gray-600 mb-6">
              {steps[currentStep].description}
            </p>
            
            <div className="space-y-3">
              <h5 className="font-semibold text-gray-900">Key Features:</h5>
              {steps[currentStep].features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Demo Actions */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary">
              Try Free Demo
            </button>
            <button className="btn-outline">
              Schedule Live Demo
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
