import { getTranslations, setRequestLocale } from 'next-intl/server';
import { ProductDemo } from '@/components/ProductDemo';

type IWordPressProductsProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IWordPressProductsProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'WordPressProducts',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

export default async function WordPressProducts(props: IWordPressProductsProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  const t = await getTranslations({
    locale,
    namespace: 'WordPressProducts',
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
      {/* Hero Section */}
      <section className="relative py-16 lg:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium mb-6">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M21.469 6.825c.84 1.537 1.318 3.3 1.318 5.175 0 3.979-2.156 7.456-5.363 9.325l3.295-9.527c.615-1.54.82-2.771.82-3.864 0-.405-.026-.78-.07-1.11m-7.981.105c.647-.03 1.232-.105 1.232-.105.582-.075.514-.93-.067-.899 0 0-1.755.135-2.88.135-1.064 0-2.85-.135-2.85-.135-.584-.031-.661.854-.067.899 0 0 .584.075 1.195.105l1.777 4.863-2.5 7.5-4.156-12.363c.649-.03 1.232-.105 1.232-.105.582-.075.516-.93-.065-.899 0 0-1.756.135-2.88.135-.202 0-.438-.008-.69-.015C4.911 2.015 8.235 0 12.001 0c2.756 0 5.27 1.021 7.186 2.7-.046-.003-.091-.009-.141-.009-1.06 0-1.812.923-1.812 1.914 0 .89.513 1.643 1.06 2.531.411.72.89 1.643.89 2.977 0 .915-.354 1.994-.821 3.479l-1.075 3.585-3.9-11.61.001.014z" />
              </svg>
              {t('hero_badge')}
            </div>
            <h1 className="text-hero text-gradient mb-6 animate-fade-in-up">
              {t('hero_title')}
            </h1>
            <p className="text-xl text-gray-600 mb-8 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
              {t('hero_description')}
            </p>

            {/* Hero Stats */}
            <div className="flex flex-col sm:flex-row gap-8 justify-center mb-12 animate-fade-in-up" style={{ animationDelay: '0.15s' }}>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{t('hero_stats_downloads')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_downloads_label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{t('hero_stats_rating')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_rating_label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">{t('hero_stats_sites')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_sites_label')}</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <button className="btn-gradient text-lg px-8 py-4">
                {t('download_free')}
              </button>
              <button className="btn-outline text-lg px-8 py-4">
                {t('view_demo')}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Product Showcase */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('products_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('products_description')}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Security Pro */}
            <div className="card-hover p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-blue-600 transition-all duration-300">
                  <svg className="w-6 h-6 text-blue-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900">{t('security_pro_title')}</h3>
                  <div className="text-sm text-blue-600 font-medium">{t('security_pro_badge')}</div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">{t('security_pro_description')}</p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('security_pro_feature_1')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('security_pro_feature_2')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('security_pro_feature_3')}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-gray-900">{t('security_pro_price')}</div>
                <button className="btn-gradient px-6 py-2">{t('get_started')}</button>
              </div>
            </div>

            {/* SEO Intelligence */}
            <div className="card-hover p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-green-600 transition-all duration-300">
                  <svg className="w-6 h-6 text-green-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900">{t('seo_intelligence_title')}</h3>
                  <div className="text-sm text-green-600 font-medium">{t('seo_intelligence_badge')}</div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">{t('seo_intelligence_description')}</p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('seo_intelligence_feature_1')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('seo_intelligence_feature_2')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('seo_intelligence_feature_3')}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-gray-900">{t('seo_intelligence_price')}</div>
                <button className="btn-gradient px-6 py-2">{t('get_started')}</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Demo Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-section text-gray-900 mb-6">
              See AppExtera in Action
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience our WordPress plugins through an interactive demo and see how they can transform your website.
            </p>
          </div>
          <ProductDemo productType="wordpress" />
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('benefits_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('benefits_description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('benefit_1_title')}</h3>
              <p className="text-gray-600">{t('benefit_1_description')}</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('benefit_2_title')}</h3>
              <p className="text-gray-600">{t('benefit_2_description')}</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('benefit_3_title')}</h3>
              <p className="text-gray-600">{t('benefit_3_description')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--color-primary)' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-section text-white mb-6">
            {t('cta_title')}
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            {t('cta_description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
            <button className="bg-white hover:bg-gray-50 font-medium px-8 py-4 rounded-lg transition-colors duration-200" style={{ color: 'var(--color-primary)' }}>
              {t('download_now')}
            </button>
            <button className="border-2 border-white text-white hover:bg-white font-medium px-8 py-4 rounded-lg transition-all duration-200">
              {t('contact_support')}
            </button>
          </div>
          <p className="text-sm text-blue-200">
            ✓ {t('money_back_guarantee')}
          </p>
        </div>
      </section>
    </div>
  );
}
