import { getTranslations, setRequestLocale } from 'next-intl/server';
import { ProductDemo } from '@/components/ProductDemo';

type IShopifyProductsProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IShopifyProductsProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'ShopifyProducts',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

export default async function ShopifyProducts(props: IShopifyProductsProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  const t = await getTranslations({
    locale,
    namespace: 'ShopifyProducts',
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-white">
      {/* Hero Section */}
      <section className="relative py-16 lg:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium mb-6">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M15.337 2.543c-.245-.315-.645-.517-1.08-.517-.435 0-.835.202-1.08.517L9.663 7.457c-.245.315-.245.719 0 1.034l3.514 4.914c.245.315.645.517 1.08.517.435 0 .835-.202 1.08-.517l3.514-4.914c.245-.315.245-.719 0-1.034L15.337 2.543zM12.257 11.486L9.663 7.457 12.257 3.428l2.594 4.029-2.594 4.029z" />
                <path d="M3.6 14.4c0-1.326 1.074-2.4 2.4-2.4s2.4 1.074 2.4 2.4-1.074 2.4-2.4 2.4-2.4-1.074-2.4-2.4zm12 0c0-1.326 1.074-2.4 2.4-2.4s2.4 1.074 2.4 2.4-1.074 2.4-2.4 2.4-2.4-1.074-2.4-2.4z" />
              </svg>
              {t('hero_badge')}
            </div>
            <h1 className="text-hero text-gradient mb-6 animate-fade-in-up">
              {t('hero_title')}
            </h1>
            <p className="text-xl text-gray-600 mb-8 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
              {t('hero_description')}
            </p>

            {/* Hero Stats */}
            <div className="flex flex-col sm:flex-row gap-8 justify-center mb-12 animate-fade-in-up" style={{ animationDelay: '0.15s' }}>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{t('hero_stats_merchants')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_merchants_label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{t('hero_stats_revenue')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_revenue_label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">{t('hero_stats_conversion')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_conversion_label')}</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <button className="btn-gradient text-lg px-8 py-4">
                {t('install_free')}
              </button>
              <button className="btn-outline text-lg px-8 py-4">
                {t('view_demo')}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Product Showcase */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('products_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('products_description')}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Subscription Manager */}
            <div className="card-hover p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-green-600 transition-all duration-300">
                  <svg className="w-6 h-6 text-green-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900">{t('subscription_manager_title')}</h3>
                  <div className="text-sm text-green-600 font-medium">{t('subscription_manager_badge')}</div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">{t('subscription_manager_description')}</p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('subscription_manager_feature_1')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('subscription_manager_feature_2')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('subscription_manager_feature_3')}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-gray-900">{t('subscription_manager_price')}</div>
                <button className="btn-gradient px-6 py-2">{t('install_now')}</button>
              </div>
            </div>

            {/* Upsell Engine */}
            <div className="card-hover p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-blue-600 transition-all duration-300">
                  <svg className="w-6 h-6 text-blue-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900">{t('upsell_engine_title')}</h3>
                  <div className="text-sm text-blue-600 font-medium">{t('upsell_engine_badge')}</div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">{t('upsell_engine_description')}</p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('upsell_engine_feature_1')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('upsell_engine_feature_2')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('upsell_engine_feature_3')}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-gray-900">{t('upsell_engine_price')}</div>
                <button className="btn-gradient px-6 py-2">{t('install_now')}</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Demo Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-section text-gray-900 mb-6">
              Experience Our Shopify Apps
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See how our Shopify apps can boost your store's revenue and streamline operations.
            </p>
          </div>
          <ProductDemo productType="shopify" />
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('success_stories_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('success_stories_description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-3xl font-bold text-green-600 mb-2">{t('success_metric_1_value')}</div>
              <div className="text-sm text-gray-600 mb-4">{t('success_metric_1_label')}</div>
              <p className="text-gray-700 text-sm">{t('success_metric_1_description')}</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-3xl font-bold text-blue-600 mb-2">{t('success_metric_2_value')}</div>
              <div className="text-sm text-gray-600 mb-4">{t('success_metric_2_label')}</div>
              <p className="text-gray-700 text-sm">{t('success_metric_2_description')}</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-3xl font-bold text-purple-600 mb-2">{t('success_metric_3_value')}</div>
              <div className="text-sm text-gray-600 mb-4">{t('success_metric_3_label')}</div>
              <p className="text-gray-700 text-sm">{t('success_metric_3_description')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('integration_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('integration_description')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-gray-600">S</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('integration_shopify')}</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-gray-600">K</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('integration_klaviyo')}</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-gray-600">M</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('integration_mailchimp')}</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-gray-600">Z</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('integration_zapier')}</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--color-primary)' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-section text-white mb-6">
            {t('cta_title')}
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            {t('cta_description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
            <button className="bg-white hover:bg-gray-50 font-medium px-8 py-4 rounded-lg transition-colors duration-200" style={{ color: 'var(--color-primary)' }}>
              {t('start_free_trial')}
            </button>
            <button className="border-2 border-white text-white hover:bg-white font-medium px-8 py-4 rounded-lg transition-all duration-200">
              {t('schedule_demo')}
            </button>
          </div>
          <p className="text-sm text-blue-200">
            ✓ {t('free_trial_guarantee')}
          </p>
        </div>
      </section>
    </div>
  );
}
