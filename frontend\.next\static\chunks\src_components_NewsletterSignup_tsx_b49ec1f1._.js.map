{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/NewsletterSignup.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations } from 'next-intl';\n\nexport function NewsletterSignup() {\n  const t = useTranslations('Blog');\n  const [email, setEmail] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubscribed, setIsSubscribed] = useState(false);\n  const [error, setError] = useState('');\n\n  const validateEmail = (email: string): boolean => {\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!email.trim()) {\n      setError('Email is required');\n      return;\n    }\n\n    if (!validateEmail(email)) {\n      setError('Please enter a valid email address');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setError('');\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      // Here you would typically send the email to your backend\n      console.log('Newsletter signup:', email);\n      \n      setIsSubscribed(true);\n      setEmail('');\n    } catch (error) {\n      console.error('Error subscribing to newsletter:', error);\n      setError('Something went wrong. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setEmail(e.target.value);\n    if (error) {\n      setError('');\n    }\n  };\n\n  if (isSubscribed) {\n    return (\n      <div className=\"max-w-4xl mx-auto text-center\">\n        <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n          </svg>\n        </div>\n        <h2 className=\"text-section text-gray-900 mb-6\">\n          Thank You for Subscribing!\n        </h2>\n        <p className=\"text-xl text-gray-600 mb-8\">\n          You'll receive our latest insights and updates directly in your inbox.\n        </p>\n        <button \n          onClick={() => setIsSubscribed(false)}\n          className=\"btn-outline\"\n        >\n          Subscribe Another Email\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto text-center\">\n      <h2 className=\"text-section text-gray-900 mb-6\">\n        {t('newsletter_title')}\n      </h2>\n      <p className=\"text-xl text-gray-600 mb-8\">\n        {t('newsletter_description')}\n      </p>\n      <form onSubmit={handleSubmit} className=\"max-w-md mx-auto\">\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <div className=\"flex-1\">\n            <input\n              type=\"email\"\n              value={email}\n              onChange={handleEmailChange}\n              placeholder={t('email_placeholder')}\n              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${\n                error ? 'border-red-500' : 'border-gray-300'\n              }`}\n              disabled={isSubmitting}\n            />\n            {error && (\n              <p className=\"mt-2 text-sm text-red-600 text-left\">{error}</p>\n            )}\n          </div>\n          <button \n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"btn-primary whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n          >\n            {isSubmitting ? (\n              <>\n                <div className=\"spinner w-5 h-5 mr-2\"></div>\n                Subscribing...\n              </>\n            ) : (\n              t('subscribe')\n            )}\n          </button>\n        </div>\n      </form>\n      <p className=\"text-sm text-gray-500 mt-4\">\n        {t('newsletter_disclaimer')}\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,gBAAgB,CAAC;QACrB,OAAO,6BAA6B,IAAI,CAAC;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,cAAc,QAAQ;YACzB,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0DAA0D;YAC1D,QAAQ,GAAG,CAAC,sBAAsB;YAElC,gBAAgB;YAChB,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,SAAS,EAAE,MAAM,CAAC,KAAK;QACvB,IAAI,OAAO;YACT,SAAS;QACX;IACF;IAEA,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAyB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAChF,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAG,WAAU;8BAAkC;;;;;;8BAGhD,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBACC,SAAS,IAAM,gBAAgB;oBAC/B,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BACX,EAAE;;;;;;0BAEL,6LAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;0BAEL,6LAAC;gBAAK,UAAU;gBAAc,WAAU;0BACtC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU;oCACV,aAAa,EAAE;oCACf,WAAW,CAAC,+GAA+G,EACzH,QAAQ,mBAAmB,mBAC3B;oCACF,UAAU;;;;;;gCAEX,uBACC,6LAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAGxD,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAA6B;;+CAI9C,EAAE;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAE,WAAU;0BACV,EAAE;;;;;;;;;;;;AAIX;GAzHgB;;QACJ,yMAAA,CAAA,kBAAe;;;KADX", "debugId": null}}]}