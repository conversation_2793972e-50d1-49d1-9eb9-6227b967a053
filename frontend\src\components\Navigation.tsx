'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { LocaleSwitcher } from '@/components/LocaleSwitcher';
import { AppConfig } from '@/utils/AppConfig';

export const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProductsOpen, setIsProductsOpen] = useState(false);
  const t = useTranslations('RootLayout');

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleProducts = () => setIsProductsOpen(!isProductsOpen);

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50 shadow-sm">
      <div className="container-wide">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <span className="text-white font-bold text-xl">A</span>
              </div>
              <span className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                {AppConfig.name}
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:block">
            <div className="ml-10 flex items-center space-x-8">
              <Link
                href="/"
                className="text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
              >
                {t('home_link')}
              </Link>

              {/* Products Dropdown */}
              <div className="relative">
                <button
                  onClick={toggleProducts}
                  className="text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 flex items-center rounded-lg hover:bg-blue-50"
                >
                  {t('products_link')}
                  <svg className={`ml-2 h-4 w-4 transition-transform duration-300 ${isProductsOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {isProductsOpen && (
                  <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-gray-100 py-4 z-50 animate-fade-in-up">
                    <Link
                      href="/products/wordpress/"
                      className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200"
                      onClick={() => setIsProductsOpen(false)}
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-blue-600 text-xs font-bold">WP</span>
                        </div>
                        <div>
                          <div className="font-medium">{t('products_wordpress')}</div>
                          <div className="text-xs text-gray-500">{t('products_wordpress_desc')}</div>
                        </div>
                      </div>
                    </Link>
                    <Link
                      href="/products/shopify/"
                      className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200"
                      onClick={() => setIsProductsOpen(false)}
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-green-600 text-xs font-bold">SH</span>
                        </div>
                        <div>
                          <div className="font-medium">{t('products_shopify')}</div>
                          <div className="text-xs text-gray-500">{t('products_shopify_desc')}</div>
                        </div>
                      </div>
                    </Link>
                    <Link
                      href="/products/browser/"
                      className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200"
                      onClick={() => setIsProductsOpen(false)}
                    >
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                          <span className="text-purple-600 text-xs font-bold">BR</span>
                        </div>
                        <div>
                          <div className="font-medium">{t('products_browser')}</div>
                          <div className="text-xs text-gray-500">{t('products_browser_desc')}</div>
                        </div>
                      </div>
                    </Link>
                  </div>
                )}
              </div>

              <Link
                href="/features/"
                className="text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
              >
                {t('features_link')}
              </Link>
              <Link
                href="/about/"
                className="text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
              >
                {t('about_link')}
              </Link>
              <Link
                href="/blog/"
                className="text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
              >
                {t('blog_link')}
              </Link>
            </div>
          </div>

          {/* Desktop Right Side */}
          <div className="hidden lg:flex items-center space-x-6">
            <LocaleSwitcher />
            <Link
              href="/contact/"
              className="text-gray-700 hover:text-blue-600 px-4 py-2 text-sm font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
            >
              {t('contact_link')}
            </Link>
            <Link
              href="/contact/"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 text-sm font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              Get Started
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-3 rounded-xl text-gray-700 hover:text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-4 pt-4 pb-6 space-y-2 bg-white border-t border-gray-200 shadow-lg">
              <Link
                href="/"
                className="text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('home_link')}
              </Link>
              <Link
                href="/features/"
                className="text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('features_link')}
              </Link>
              <Link
                href="/about/"
                className="text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('about_link')}
              </Link>
              <Link
                href="/blog/"
                className="text-gray-700 hover:text-blue-600 block px-4 py-3 text-base font-semibold transition-colors duration-300 rounded-lg hover:bg-blue-50"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('blog_link')}
              </Link>
              <div className="border-t border-gray-200 pt-6 mt-4">
                <div className="flex items-center px-3 space-x-3 mb-4">
                  <LocaleSwitcher />
                </div>
                <Link
                  href="/contact/"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white block mx-3 py-3 text-center font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};
