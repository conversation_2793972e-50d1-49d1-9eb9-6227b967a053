import Head from 'next/head';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  siteName?: string;
  locale?: string;
  alternateLocales?: string[];
}

export function SEOHead({
  title = 'AppExtera - Professional WordPress, Shopify & Browser Extensions',
  description = 'Boost your website performance with AppExtera\'s premium WordPress plugins, Shopify apps, and browser extensions. Trusted by thousands of businesses worldwide.',
  keywords = ['WordPress plugins', 'Shopify apps', 'browser extensions', 'website optimization', 'e-commerce tools'],
  image = '/images/og-image.jpg',
  url = 'https://appextera.com',
  type = 'website',
  publishedTime,
  modifiedTime,
  author = 'AppExtera Team',
  siteName = 'AppExtera',
  locale = 'en_US',
  alternateLocales = ['fr_FR']
}: SEOHeadProps) {
  const fullTitle = title.includes('AppExtera') ? title : `${title} | AppExtera`;
  const fullImageUrl = image.startsWith('http') ? image : `${url}${image}`;

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      
      {/* Alternate Locales */}
      {alternateLocales.map((altLocale) => (
        <meta key={altLocale} property="og:locale:alternate" content={altLocale} />
      ))}
      
      {/* Article specific meta tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && (
        <meta property="article:author" content={author} />
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:site" content="@AppExtera" />
      <meta name="twitter:creator" content="@AppExtera" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#3B82F6" />
      <meta name="msapplication-TileColor" content="#3B82F6" />
      
      {/* Favicon */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": type === 'article' ? 'Article' : 'Organization',
            ...(type === 'article' ? {
              headline: title,
              description: description,
              image: fullImageUrl,
              author: {
                "@type": "Person",
                name: author
              },
              publisher: {
                "@type": "Organization",
                name: siteName,
                logo: {
                  "@type": "ImageObject",
                  url: `${url}/images/logo.png`
                }
              },
              datePublished: publishedTime,
              dateModified: modifiedTime || publishedTime
            } : {
              name: siteName,
              url: url,
              logo: `${url}/images/logo.png`,
              description: description,
              sameAs: [
                "https://twitter.com/AppExtera",
                "https://linkedin.com/company/appextera",
                "https://github.com/appextera"
              ]
            })
          })
        }}
      />
    </Head>
  );
}
