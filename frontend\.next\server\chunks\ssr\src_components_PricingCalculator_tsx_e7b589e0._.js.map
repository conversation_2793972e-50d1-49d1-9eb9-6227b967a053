{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/PricingCalculator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface PricingTier {\n  id: string;\n  name: string;\n  basePrice: number;\n  features: string[];\n  popular?: boolean;\n}\n\nconst pricingTiers: PricingTier[] = [\n  {\n    id: 'free',\n    name: 'Free',\n    basePrice: 0,\n    features: [\n      'Basic browser extension',\n      'Up to 100 orders/month',\n      'Email support',\n      'Basic analytics'\n    ]\n  },\n  {\n    id: 'pro',\n    name: 'Pro',\n    basePrice: 29,\n    popular: true,\n    features: [\n      'Advanced analytics dashboard',\n      'Unlimited orders',\n      'Priority support',\n      'Team collaboration',\n      'Custom integrations',\n      'Advanced reporting'\n    ]\n  },\n  {\n    id: 'enterprise',\n    name: 'Enterprise',\n    basePrice: 99,\n    features: [\n      'Custom integrations',\n      'Dedicated account manager',\n      'SLA guarantee',\n      'Advanced security features',\n      'White-label options',\n      'Custom development'\n    ]\n  }\n];\n\nexport function PricingCalculator() {\n  const [selectedTier, setSelectedTier] = useState('pro');\n  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');\n  const [teamSize, setTeamSize] = useState(5);\n\n  const calculatePrice = (tier: PricingTier) => {\n    let price = tier.basePrice;\n    \n    // Apply team size multiplier for Pro and Enterprise\n    if (tier.id === 'pro' && teamSize > 5) {\n      price += (teamSize - 5) * 5; // $5 per additional user\n    } else if (tier.id === 'enterprise' && teamSize > 10) {\n      price += (teamSize - 10) * 10; // $10 per additional user\n    }\n    \n    // Apply yearly discount\n    if (billingCycle === 'yearly') {\n      price = Math.round(price * 12 * 0.8); // 20% discount for yearly\n    }\n    \n    return price;\n  };\n\n  const getYearlySavings = (tier: PricingTier) => {\n    const monthlyTotal = calculatePrice(tier) * 12;\n    const yearlyPrice = calculatePrice({ ...tier, basePrice: tier.basePrice });\n    const yearlyTotal = Math.round(yearlyPrice * 12 * 0.8);\n    return monthlyTotal - yearlyTotal;\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto\">\n      {/* Billing Toggle */}\n      <div className=\"flex justify-center mb-12\">\n        <div className=\"bg-gray-100 p-1 rounded-lg\">\n          <button\n            onClick={() => setBillingCycle('monthly')}\n            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${\n              billingCycle === 'monthly'\n                ? 'bg-white text-gray-900 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Monthly\n          </button>\n          <button\n            onClick={() => setBillingCycle('yearly')}\n            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${\n              billingCycle === 'yearly'\n                ? 'bg-white text-gray-900 shadow-sm'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            Yearly\n            <span className=\"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n              Save 20%\n            </span>\n          </button>\n        </div>\n      </div>\n\n      {/* Team Size Selector */}\n      <div className=\"text-center mb-12\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-4\">\n          Team Size: {teamSize} {teamSize === 1 ? 'user' : 'users'}\n        </label>\n        <div className=\"max-w-md mx-auto\">\n          <input\n            type=\"range\"\n            min=\"1\"\n            max=\"50\"\n            value={teamSize}\n            onChange={(e) => setTeamSize(Number(e.target.value))}\n            className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n          />\n          <div className=\"flex justify-between text-xs text-gray-500 mt-2\">\n            <span>1</span>\n            <span>25</span>\n            <span>50+</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Pricing Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n        {pricingTiers.map((tier) => {\n          const price = calculatePrice(tier);\n          const isSelected = selectedTier === tier.id;\n          \n          return (\n            <div\n              key={tier.id}\n              className={`card-hover p-8 relative cursor-pointer transition-all ${\n                tier.popular ? 'border-2 border-blue-500' : ''\n              } ${\n                isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''\n              }`}\n              onClick={() => setSelectedTier(tier.id)}\n            >\n              {tier.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <span className=\"bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium\">\n                    Most Popular\n                  </span>\n                </div>\n              )}\n              \n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  {tier.name}\n                </h3>\n                <div className=\"mb-6\">\n                  <span className=\"text-4xl font-bold text-gray-900\">\n                    ${price}\n                  </span>\n                  <span className=\"text-gray-600 ml-2\">\n                    {billingCycle === 'yearly' ? '/year' : '/month'}\n                  </span>\n                  {billingCycle === 'yearly' && tier.basePrice > 0 && (\n                    <div className=\"text-sm text-green-600 mt-1\">\n                      Save ${getYearlySavings(tier)} per year\n                    </div>\n                  )}\n                </div>\n                \n                <button \n                  className={`w-full mb-8 ${\n                    tier.id === 'free' \n                      ? 'btn-outline' \n                      : isSelected \n                        ? 'btn-primary' \n                        : 'btn-secondary'\n                  }`}\n                >\n                  {tier.id === 'free' ? 'Get Started Free' : \n                   tier.id === 'enterprise' ? 'Contact Sales' : 'Start Free Trial'}\n                </button>\n                \n                <ul className=\"space-y-4 text-left\">\n                  {tier.features.map((feature, index) => (\n                    <li key={index} className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 text-green-500 mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-600\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Pricing Notes */}\n      <div className=\"mt-12 text-center\">\n        <div className=\"bg-gray-50 rounded-lg p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-2\">Pricing Notes</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li>• Pro plan includes 5 users, additional users $5/month each</li>\n            <li>• Enterprise plan includes 10 users, additional users $10/month each</li>\n            <li>• All plans include 30-day money-back guarantee</li>\n            <li>• Yearly plans are billed annually with 20% discount</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,eAA8B;IAClC;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,KAAK,SAAS;QAE1B,oDAAoD;QACpD,IAAI,KAAK,EAAE,KAAK,SAAS,WAAW,GAAG;YACrC,SAAS,CAAC,WAAW,CAAC,IAAI,GAAG,yBAAyB;QACxD,OAAO,IAAI,KAAK,EAAE,KAAK,gBAAgB,WAAW,IAAI;YACpD,SAAS,CAAC,WAAW,EAAE,IAAI,IAAI,0BAA0B;QAC3D;QAEA,wBAAwB;QACxB,IAAI,iBAAiB,UAAU;YAC7B,QAAQ,KAAK,KAAK,CAAC,QAAQ,KAAK,MAAM,0BAA0B;QAClE;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,eAAe,QAAQ;QAC5C,MAAM,cAAc,eAAe;YAAE,GAAG,IAAI;YAAE,WAAW,KAAK,SAAS;QAAC;QACxE,MAAM,cAAc,KAAK,KAAK,CAAC,cAAc,KAAK;QAClD,OAAO,eAAe;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,2DAA2D,EACrE,iBAAiB,YACb,qCACA,qCACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,2DAA2D,EACrE,iBAAiB,WACb,qCACA,qCACJ;;gCACH;8CAEC,8OAAC;oCAAK,WAAU;8CAAkE;;;;;;;;;;;;;;;;;;;;;;;0BAQxF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;;4BAA+C;4BAClD;4BAAS;4BAAE,aAAa,IAAI,SAAS;;;;;;;kCAEnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;gCAClD,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC;oBACjB,MAAM,QAAQ,eAAe;oBAC7B,MAAM,aAAa,iBAAiB,KAAK,EAAE;oBAE3C,qBACE,8OAAC;wBAEC,WAAW,CAAC,sDAAsD,EAChE,KAAK,OAAO,GAAG,6BAA6B,GAC7C,CAAC,EACA,aAAa,yCAAyC,IACtD;wBACF,SAAS,IAAM,gBAAgB,KAAK,EAAE;;4BAErC,KAAK,OAAO,kBACX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAoE;;;;;;;;;;;0CAMxF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,KAAK,IAAI;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDAAmC;oDAC/C;;;;;;;0DAEJ,8OAAC;gDAAK,WAAU;0DACb,iBAAiB,WAAW,UAAU;;;;;;4CAExC,iBAAiB,YAAY,KAAK,SAAS,GAAG,mBAC7C,8OAAC;gDAAI,WAAU;;oDAA8B;oDACpC,iBAAiB;oDAAM;;;;;;;;;;;;;kDAKpC,8OAAC;wCACC,WAAW,CAAC,YAAY,EACtB,KAAK,EAAE,KAAK,SACR,gBACA,aACE,gBACA,iBACN;kDAED,KAAK,EAAE,KAAK,SAAS,qBACrB,KAAK,EAAE,KAAK,eAAe,kBAAkB;;;;;;kDAGhD,8OAAC;wCAAG,WAAU;kDACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;gDAAe,WAAU;;kEACxB,8OAAC;wDAAI,WAAU;wDAA4C,MAAK;wDAAe,SAAQ;kEACrF,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;kEAE3J,8OAAC;wDAAK,WAAU;kEAAiB;;;;;;;+CAJ1B;;;;;;;;;;;;;;;;;uBAjDV,KAAK,EAAE;;;;;gBA4DlB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}]}