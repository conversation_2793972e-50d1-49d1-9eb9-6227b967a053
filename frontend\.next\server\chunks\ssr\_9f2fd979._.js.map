{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/NewsletterSignup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NewsletterSignup = registerClientReference(\n    function() { throw new Error(\"Attempted to call NewsletterSignup() from the server but NewsletterSignup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NewsletterSignup.tsx <module evaluation>\",\n    \"NewsletterSignup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/NewsletterSignup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NewsletterSignup = registerClientReference(\n    function() { throw new Error(\"Attempted to call NewsletterSignup() from the server but NewsletterSignup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NewsletterSignup.tsx\",\n    \"NewsletterSignup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/BlogListing.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogListing = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogListing() from the server but BlogListing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/BlogListing.tsx <module evaluation>\",\n    \"BlogListing\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/BlogListing.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogListing = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogListing() from the server but BlogListing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/BlogListing.tsx\",\n    \"BlogListing\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/app/%5Blocale%5D/%28marketing%29/blog/page.tsx"], "sourcesContent": ["import { getTranslations, setRequestLocale } from 'next-intl/server';\nimport { NewsletterSignup } from '@/components/NewsletterSignup';\nimport { BlogListing } from '@/components/BlogListing';\n\ntype IBlogProps = {\n  params: Promise<{ locale: string }>;\n};\n\nexport async function generateMetadata(props: IBlogProps) {\n  const { locale } = await props.params;\n  const t = await getTranslations({\n    locale,\n    namespace: 'Blog',\n  });\n\n  return {\n    title: t('meta_title'),\n    description: t('meta_description'),\n  };\n}\n\nexport default async function Blog(props: IBlogProps) {\n  const { locale } = await props.params;\n  setRequestLocale(locale);\n  const t = await getTranslations({\n    locale,\n    namespace: 'Blog',\n  });\n\n  // Mock blog posts data - will be replaced with CMS data\n  const blogPosts = [\n    {\n      id: 1,\n      title: 'Getting Started with AppExtera Browser Extension',\n      excerpt: 'Learn how to install and configure the AppExtera browser extension for your e-commerce store.',\n      category: 'Tutorial',\n      readTime: '5 min read',\n      publishedAt: '2024-01-15',\n      image: '/assets/blog/blog-1.jpg',\n    },\n    {\n      id: 2,\n      title: 'Advanced Analytics for E-commerce Success',\n      excerpt: 'Discover how to leverage AppExtera\\'s analytics features to boost your online store performance.',\n      category: 'Analytics',\n      readTime: '8 min read',\n      publishedAt: '2024-01-12',\n      image: '/assets/blog/blog-2.jpg',\n    },\n    {\n      id: 3,\n      title: 'Shopify Integration Best Practices',\n      excerpt: 'Maximize your Shopify store efficiency with these AppExtera integration tips and tricks.',\n      category: 'Integration',\n      readTime: '6 min read',\n      publishedAt: '2024-01-10',\n      image: '/assets/blog/blog-3.jpg',\n    },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-white\">\n      {/* Hero Section */}\n      <section className=\"relative py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center\">\n            <h1 className=\"text-hero text-gradient mb-6\">\n              {t('hero_title')}\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-12\">\n              {t('hero_description')}\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Post */}\n      <section className=\"py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"card-hover overflow-hidden\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2\">\n              <div className=\"aspect-w-16 aspect-h-9 lg:aspect-none lg:h-full\">\n                <div className=\"w-full h-64 lg:h-full gradient-primary flex items-center justify-center\">\n                  <span className=\"text-white text-lg font-medium\">Featured Image</span>\n                </div>\n              </div>\n              <div className=\"p-8 lg:p-12\">\n                <div className=\"flex items-center mb-4\">\n                  <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3\">\n                    Featured\n                  </span>\n                  <span className=\"text-gray-500 text-sm\">8 min read</span>\n                </div>\n                <h2 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-4\">\n                  {t('featured_post_title')}\n                </h2>\n                <p className=\"text-gray-600 mb-6\">\n                  {t('featured_post_excerpt')}\n                </p>\n                <button className=\"btn-primary\">\n                  {t('read_more')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Blog Posts Grid */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <BlogListing />\n      </section>\n\n      {/* Newsletter Signup */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-gray-50\">\n        <NewsletterSignup />\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;AACA;;;;;AAMO,eAAe,iBAAiB,KAAiB;IACtD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;AAEe,eAAe,KAAK,KAAiB;IAClD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,MAAM;IACrC,CAAA,GAAA,2QAAA,CAAA,mBAAgB,AAAD,EAAE;IACjB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAC9B;QACA,WAAW;IACb;IAEA,wDAAwD;IACxD,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4E;;;;;;8DAG5F,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;sDAEL,8OAAC;4CAAO,WAAU;sDACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC,iIAAA,CAAA,cAAW;;;;;;;;;;0BAId,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC,sIAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;AAIzB", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js"], "sourcesContent": ["import { cache } from 'react';\nimport getConfig from './getConfig.js';\nimport getServerTranslator from './getServerTranslator.js';\n\n// Maintainer note: `getTranslations` has two different call signatures.\n// We need to define these with function overloads, otherwise TypeScript\n// messes up the return type.\n\n// Call signature 1: `getTranslations(namespace)`\n\n// Call signature 2: `getTranslations({locale, namespace})`\n\n// Implementation\nasync function getTranslations(namespaceOrOpts) {\n  let namespace;\n  let locale;\n  if (typeof namespaceOrOpts === 'string') {\n    namespace = namespaceOrOpts;\n  } else if (namespaceOrOpts) {\n    locale = namespaceOrOpts.locale;\n    namespace = namespaceOrOpts.namespace;\n  }\n  const config = await getConfig(locale);\n  return getServerTranslator(config, namespace);\n}\nvar getTranslations$1 = cache(getTranslations);\n\nexport { getTranslations$1 as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,wEAAwE;AACxE,wEAAwE;AACxE,6BAA6B;AAE7B,iDAAiD;AAEjD,2DAA2D;AAE3D,iBAAiB;AACjB,eAAe,gBAAgB,eAAe;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,oBAAoB,UAAU;QACvC,YAAY;IACd,OAAO,IAAI,iBAAiB;QAC1B,SAAS,gBAAgB,MAAM;QAC/B,YAAY,gBAAgB,SAAS;IACvC;IACA,MAAM,SAAS,MAAM,CAAA,GAAA,oMAAA,CAAA,UAAS,AAAD,EAAE;IAC/B,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAmB,AAAD,EAAE,QAAQ;AACrC;AACA,IAAI,oBAAoB,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG,cAAc;oBAAA,GAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;wCACVC,IAAAA;oCAAAA,CAAMZ,UAAUa;oCAAAA,OAAQ;yCACxBC,MAAM;8CACNC,IAAAA,CAAAA,GAAU;wCAAA,QAAA;4CAAA,IAAA;4CAAA;yCAAA;;uCACV,2CAA2C;;iCAC3CC,YAAY;sCACZC,IAAAA,CAAAA;4BAAAA,CAAU;yBAAA;;yBACVC,UAAU,EAAE;0BACd,QAAA,CAAA;wBAAA,UAAA;4BAAA,IAAA;4BAAA;yBAAA;;mBACAC,UAAU;;iBACRC,YAAYnB;kBACd,QAAA,CAAA;gBAAA,UAAA;oBAAA,IAAA;oBAAA;iBAAA;YACF;SAAA,CAAE", "ignoreList": [0], "debugId": null}}]}