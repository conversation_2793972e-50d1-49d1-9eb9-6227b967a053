/* Import fonts first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* Import TailwindCSS */
@import 'tailwindcss';

/* AppExtera Global Styles */
:root {
    /* AppExtera Brand Colors */
    --color-primary: #0ea5e9;
    --color-primary-dark: #0284c7;
    --color-primary-light: #38bdf8;
    --color-secondary: #64748b;
    --color-accent: #d026d7;
    --color-success: #22c55e;
    --color-warning: #f59e0b;
    --color-error: #ef4444;

    /* Secondary Color Scale for Footer and UI */
    --color-secondary-50: #f8fafc;
    --color-secondary-100: #f1f5f9;
    --color-secondary-200: #e2e8f0;
    --color-secondary-300: #cbd5e1;
    --color-secondary-400: #94a3b8;
    --color-secondary-500: #64748b;
    --color-secondary-600: #475569;
    --color-secondary-700: #334155;
    --color-secondary-800: #1e293b;
    --color-secondary-900: #0f172a;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #0ea5e9 0%, #d026d7 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);

    /* Shadows */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-large: 0 10px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 10px 40px -10px rgba(14, 165, 233, 0.3);

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
    :root {
        --color-primary: #38bdf8;
        --color-primary-dark: #0ea5e9;
    }
}

/* Base styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Custom utility classes */
.gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.gradient-secondary {
    background: var(--gradient-secondary);
}

.bg-gradient-secondary {
    background: var(--gradient-secondary);
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: var(--shadow-soft);
}

.shadow-colored {
    box-shadow: var(--shadow-colored);
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .rtl\:text-right {
    text-align: right;
}

[dir="rtl"] .rtl\:text-left {
    text-align: left;
}

/* Modern Button Components */
.btn-primary {
    background-color: var(--color-primary);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-soft);
}

.btn-primary:hover {
    background-color: var(--color-primary-dark);
    box-shadow: var(--shadow-medium);
}

.btn-primary:focus {
    outline: none;
    background-color: var(--color-primary-dark);
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px rgba(14, 165, 233, 0.2);
}

.btn-secondary {
    background-color: #f1f5f9;
    color: #0f172a;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-soft);
}

.btn-secondary:hover {
    background-color: #e2e8f0;
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: 2px solid var(--color-primary);
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: white;
}

.btn-outline:focus {
    outline: none;
    background-color: var(--color-primary);
    color: white;
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px rgba(14, 165, 233, 0.2);
}

.btn-gradient {
    background: var(--gradient-primary);
    color: white;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-colored);
}

.btn-gradient:hover {
    opacity: 0.9;
    box-shadow: var(--shadow-large);
}

.btn-gradient:focus {
    outline: none;
    opacity: 0.9;
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 4px rgba(14, 165, 233, 0.2);
}

/* Modern Card Components */
.card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.card-hover {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card-hover:hover {
    box-shadow: var(--shadow-medium);
    border-color: #cbd5e1;
}

.card-interactive {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-soft);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    overflow: hidden;
    cursor: pointer;
}

.card-interactive:hover {
    box-shadow: var(--shadow-medium);
    border-color: #cbd5e1;
    transform: translateY(-4px);
}

.card-gradient {
    background: var(--gradient-primary);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-colored);
    border: none;
    color: white;
    overflow: hidden;
}

/* Animation utilities */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner */
.spinner {
    animation: spin 1s linear infinite;
    border-radius: 50%;
    border: 2px solid #d1d5db;
    border-top-color: var(--color-primary);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Modern Typography System */
.text-hero {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

@media (min-width: 768px) {
    .text-hero {
        font-size: 3rem;
    }
}

@media (min-width: 1024px) {
    .text-hero {
        font-size: 3.75rem;
    }
}

.text-section {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.25;
}

@media (min-width: 768px) {
    .text-section {
        font-size: 1.875rem;
    }
}

@media (min-width: 1024px) {
    .text-section {
        font-size: 2.25rem;
    }
}

.text-subsection {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.375;
}

@media (min-width: 768px) {
    .text-subsection {
        font-size: 1.5rem;
    }
}

.text-balance {
    text-wrap: balance;
}

/* Modern Layout Utilities */
.container-wide {
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container-wide {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container-wide {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.container-narrow {
    max-width: 56rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 640px) {
    .container-narrow {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container-narrow {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

.section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

@media (min-width: 1024px) {
    .section-padding {
        padding-top: 6rem;
        padding-bottom: 6rem;
    }
}

.section-padding-sm {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

@media (min-width: 1024px) {
    .section-padding-sm {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}

/* Performance optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus management for accessibility */
.focus-visible:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Text utilities */
.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}

/* Image optimization */
img {
    max-width: 100%;
    height: auto;
}

.lazy-image {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy-image.loaded {
    opacity: 1;
}

/* Skeleton loading animation */
@keyframes skeleton-loading {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
}

/* Improved form styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:invalid {
    border-color: #ef4444;
}

.form-input:invalid:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-error {
    font-size: 0.875rem;
    color: #ef4444;
    margin-top: 0.25rem;
}

/* Enhanced button loading state */
.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive utilities */
.aspect-video {
    aspect-ratio: 16 / 9;
}

.aspect-square {
    aspect-ratio: 1 / 1;
}

/* Secondary Color Utilities */
.bg-secondary-50 {
    background-color: var(--color-secondary-50);
}

.bg-secondary-100 {
    background-color: var(--color-secondary-100);
}

.bg-secondary-200 {
    background-color: var(--color-secondary-200);
}

.bg-secondary-300 {
    background-color: var(--color-secondary-300);
}

.bg-secondary-400 {
    background-color: var(--color-secondary-400);
}

.bg-secondary-500 {
    background-color: var(--color-secondary-500);
}

.bg-secondary-600 {
    background-color: var(--color-secondary-600);
}

.bg-secondary-700 {
    background-color: var(--color-secondary-700);
}

.bg-secondary-800 {
    background-color: var(--color-secondary-800);
}

.bg-secondary-900 {
    background-color: var(--color-secondary-900);
}

.text-secondary-50 {
    color: var(--color-secondary-50);
}

.text-secondary-100 {
    color: var(--color-secondary-100);
}

.text-secondary-200 {
    color: var(--color-secondary-200);
}

.text-secondary-300 {
    color: var(--color-secondary-300);
}

.text-secondary-400 {
    color: var(--color-secondary-400);
}

.text-secondary-500 {
    color: var(--color-secondary-500);
}

.text-secondary-600 {
    color: var(--color-secondary-600);
}

.text-secondary-700 {
    color: var(--color-secondary-700);
}

.text-secondary-800 {
    color: var(--color-secondary-800);
}

.text-secondary-900 {
    color: var(--color-secondary-900);
}

.border-secondary-50 {
    border-color: var(--color-secondary-50);
}

.border-secondary-100 {
    border-color: var(--color-secondary-100);
}

.border-secondary-200 {
    border-color: var(--color-secondary-200);
}

.border-secondary-300 {
    border-color: var(--color-secondary-300);
}

.border-secondary-400 {
    border-color: var(--color-secondary-400);
}

.border-secondary-500 {
    border-color: var(--color-secondary-500);
}

.border-secondary-600 {
    border-color: var(--color-secondary-600);
}

.border-secondary-700 {
    border-color: var(--color-secondary-700);
}

.border-secondary-800 {
    border-color: var(--color-secondary-800);
}

.border-secondary-900 {
    border-color: var(--color-secondary-900);
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .card,
    .card-hover,
    .card-interactive {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}