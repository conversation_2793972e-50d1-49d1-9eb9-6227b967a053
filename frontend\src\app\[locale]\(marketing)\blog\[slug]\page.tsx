import { getTranslations, setRequestLocale } from 'next-intl/server';
import { notFound } from 'next/navigation';

interface BlogArticlePageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

// Mock blog post data - in a real app, this would come from a CMS or database
const getBlogPost = async (slug: string) => {
  const posts = {
    'wordpress-security-best-practices': {
      id: 1,
      title: 'WordPress Security Best Practices for 2024',
      excerpt: 'Learn the essential security measures to protect your WordPress site from threats and vulnerabilities.',
      content: `
        <h2>Introduction</h2>
        <p>WordPress security is more important than ever in 2024. With cyber threats evolving constantly, it's crucial to implement robust security measures to protect your website and user data.</p>
        
        <h2>Essential Security Measures</h2>
        <h3>1. Keep Everything Updated</h3>
        <p>Regular updates are your first line of defense against security vulnerabilities. This includes:</p>
        <ul>
          <li>WordPress core updates</li>
          <li>Plugin updates</li>
          <li>Theme updates</li>
          <li>PHP version updates</li>
        </ul>
        
        <h3>2. Use Strong Authentication</h3>
        <p>Implement two-factor authentication and enforce strong password policies for all users.</p>
        
        <h3>3. Regular Backups</h3>
        <p>Automated daily backups ensure you can quickly recover from any security incident.</p>
        
        <h2>Advanced Security Features</h2>
        <p>Our AppExtera Security Plugin provides advanced features including:</p>
        <ul>
          <li>Real-time malware scanning</li>
          <li>Firewall protection</li>
          <li>Login attempt monitoring</li>
          <li>File integrity monitoring</li>
        </ul>
        
        <h2>Conclusion</h2>
        <p>Implementing these security best practices will significantly improve your WordPress site's protection against threats. For comprehensive security coverage, consider using our AppExtera Security Plugin.</p>
      `,
      category: 'Security',
      readTime: '8 min read',
      publishedAt: '2024-01-15',
      author: {
        name: 'John Doe',
        role: 'Security Expert',
        avatar: '/images/authors/john-doe.jpg'
      },
      tags: ['WordPress', 'Security', 'Best Practices', 'Malware Protection'],
      image: '/images/blog/wordpress-security.jpg'
    },
    'shopify-conversion-optimization': {
      id: 2,
      title: 'Shopify Conversion Optimization: Boost Your Sales by 40%',
      excerpt: 'Discover proven strategies to optimize your Shopify store for higher conversions and increased revenue.',
      content: `
        <h2>Understanding Conversion Optimization</h2>
        <p>Conversion optimization is the process of improving your Shopify store to increase the percentage of visitors who complete desired actions, such as making a purchase.</p>
        
        <h2>Key Optimization Strategies</h2>
        <h3>1. Optimize Product Pages</h3>
        <p>Your product pages are crucial for conversions. Focus on:</p>
        <ul>
          <li>High-quality product images</li>
          <li>Compelling product descriptions</li>
          <li>Customer reviews and ratings</li>
          <li>Clear pricing and shipping information</li>
        </ul>
        
        <h3>2. Streamline Checkout Process</h3>
        <p>Reduce cart abandonment by simplifying your checkout process and offering multiple payment options.</p>
        
        <h3>3. Implement Upselling and Cross-selling</h3>
        <p>Use our AppExtera Upsell Engine to automatically suggest relevant products and increase average order value.</p>
        
        <h2>Measuring Success</h2>
        <p>Track key metrics including conversion rate, average order value, and customer lifetime value to measure the impact of your optimization efforts.</p>
      `,
      category: 'Analytics',
      readTime: '6 min read',
      publishedAt: '2024-01-10',
      author: {
        name: 'Sarah Smith',
        role: 'E-commerce Specialist',
        avatar: '/images/authors/sarah-smith.jpg'
      },
      tags: ['Shopify', 'Conversion', 'E-commerce', 'Sales'],
      image: '/images/blog/shopify-conversion.jpg'
    }
  };

  return posts[slug as keyof typeof posts] || null;
};

export default async function BlogArticlePage({ params }: BlogArticlePageProps) {
  const { locale, slug } = await params;
  setRequestLocale(locale);
  
  const t = await getTranslations('Blog');
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Article Header */}
      <header className="bg-gray-50 py-16 lg:py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-6">
              <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium mr-3">
                {post.category}
              </span>
              <span className="text-gray-500 text-sm">{post.readTime}</span>
            </div>
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              {post.title}
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              {post.excerpt}
            </p>
            <div className="flex items-center justify-center">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold">
                    {post.author.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div className="text-left">
                  <p className="font-semibold text-gray-900">{post.author.name}</p>
                  <p className="text-sm text-gray-600">{post.author.role}</p>
                </div>
              </div>
              <span className="mx-4 text-gray-300">•</span>
              <time className="text-gray-600">
                {new Date(post.publishedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
            </div>
          </div>
        </div>
      </header>

      {/* Article Content */}
      <article className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Featured Image */}
          <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-12 flex items-center justify-center">
            <span className="text-gray-600 font-medium">Featured Image</span>
          </div>

          {/* Article Body */}
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />

          {/* Tags */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 cursor-pointer transition-colors"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </article>

      {/* Related Articles */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
            Related Articles
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Related article cards would go here */}
            <div className="card-hover p-6">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg mb-4 flex items-center justify-center">
                <span className="text-blue-600 font-medium">Related Article</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                WordPress Performance Optimization
              </h3>
              <p className="text-gray-600 mb-4">
                Learn how to speed up your WordPress site and improve user experience.
              </p>
              <button className="text-blue-600 hover:text-blue-700 font-medium text-sm">
                Read More
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter CTA */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Stay Updated with Our Latest Insights
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Get the latest tips, tutorials, and industry insights delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-300"
            />
            <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
