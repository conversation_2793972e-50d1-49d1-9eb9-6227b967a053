{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/my%20companys/AppExtera/frontend/src/components/ProductDemo.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface DemoStep {\n  id: string;\n  title: string;\n  description: string;\n  image?: string;\n  features: string[];\n}\n\ninterface ProductDemoProps {\n  productType: 'wordpress' | 'shopify' | 'browser';\n}\n\nconst demoSteps = {\n  wordpress: [\n    {\n      id: 'security-scan',\n      title: 'Security Scan',\n      description: 'AI-powered threat detection scans your entire WordPress site for vulnerabilities and malware.',\n      features: [\n        'Real-time malware detection',\n        'Vulnerability assessment',\n        'File integrity monitoring',\n        'Suspicious activity alerts'\n      ]\n    },\n    {\n      id: 'performance-optimization',\n      title: 'Performance Optimization',\n      description: 'Automatic performance optimization with intelligent caching and code minification.',\n      features: [\n        'Smart caching system',\n        'Image optimization',\n        'Code minification',\n        'Database cleanup'\n      ]\n    },\n    {\n      id: 'seo-analysis',\n      title: 'SEO Analysis',\n      description: 'Comprehensive SEO analysis with actionable recommendations for better rankings.',\n      features: [\n        'Content optimization',\n        'Meta tag analysis',\n        'Schema markup',\n        'Competitor insights'\n      ]\n    }\n  ],\n  shopify: [\n    {\n      id: 'subscription-management',\n      title: 'Subscription Management',\n      description: 'Advanced subscription management with flexible billing and customer retention tools.',\n      features: [\n        'Flexible billing cycles',\n        'Churn prediction',\n        'Customer portal',\n        'Automated renewals'\n      ]\n    },\n    {\n      id: 'upsell-engine',\n      title: 'Upsell Engine',\n      description: 'AI-powered product recommendations that increase average order value.',\n      features: [\n        'Smart recommendations',\n        'Post-purchase upsells',\n        'Bundle suggestions',\n        'A/B testing'\n      ]\n    },\n    {\n      id: 'analytics-dashboard',\n      title: 'Analytics Dashboard',\n      description: 'Comprehensive analytics dashboard with revenue insights and performance metrics.',\n      features: [\n        'Revenue tracking',\n        'Customer insights',\n        'Performance metrics',\n        'Custom reports'\n      ]\n    }\n  ],\n  browser: [\n    {\n      id: 'tab-management',\n      title: 'Tab Management',\n      description: 'Intelligent tab organization and management for improved productivity.',\n      features: [\n        'Auto-grouping tabs',\n        'Tab search',\n        'Session saving',\n        'Memory optimization'\n      ]\n    },\n    {\n      id: 'shopping-assistant',\n      title: 'Shopping Assistant',\n      description: 'Automatic coupon discovery and price tracking for online shopping.',\n      features: [\n        'Coupon discovery',\n        'Price tracking',\n        'Deal alerts',\n        'Cashback rewards'\n      ]\n    },\n    {\n      id: 'productivity-tools',\n      title: 'Productivity Tools',\n      description: 'Suite of productivity tools to enhance your browsing experience.',\n      features: [\n        'Website blocking',\n        'Focus mode',\n        'Quick notes',\n        'Bookmark sync'\n      ]\n    }\n  ]\n};\n\nexport function ProductDemo({ productType }: ProductDemoProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const steps = demoSteps[productType];\n\n  const handlePlayDemo = () => {\n    setIsPlaying(true);\n    setCurrentStep(0);\n    \n    // Auto-advance through steps\n    const interval = setInterval(() => {\n      setCurrentStep((prev) => {\n        if (prev >= steps.length - 1) {\n          setIsPlaying(false);\n          clearInterval(interval);\n          return 0;\n        }\n        return prev + 1;\n      });\n    }, 3000);\n  };\n\n  const handleStepClick = (index: number) => {\n    setCurrentStep(index);\n    setIsPlaying(false);\n  };\n\n  return (\n    <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\n      {/* Demo Header */}\n      <div className=\"bg-gray-900 text-white p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-xl font-semibold mb-2\">Interactive Demo</h3>\n            <p className=\"text-gray-300\">See how our {productType} solution works</p>\n          </div>\n          <button\n            onClick={handlePlayDemo}\n            disabled={isPlaying}\n            className=\"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-6 py-3 rounded-lg font-medium transition-colors flex items-center\"\n          >\n            {isPlaying ? (\n              <>\n                <div className=\"spinner w-5 h-5 mr-2\"></div>\n                Playing...\n              </>\n            ) : (\n              <>\n                <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n                </svg>\n                Play Demo\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Demo Content */}\n      <div className=\"p-6\">\n        {/* Step Navigation */}\n        <div className=\"flex space-x-2 mb-6 overflow-x-auto\">\n          {steps.map((step, index) => (\n            <button\n              key={step.id}\n              onClick={() => handleStepClick(index)}\n              className={`flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                index === currentStep\n                  ? 'bg-blue-100 text-blue-800'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n              }`}\n            >\n              {index + 1}. {step.title}\n            </button>\n          ))}\n        </div>\n\n        {/* Current Step Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Demo Visualization */}\n          <div className=\"relative\">\n            <div className=\"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <p className=\"text-gray-600 font-medium\">{steps[currentStep].title} Demo</p>\n                <p className=\"text-sm text-gray-500 mt-2\">Interactive visualization</p>\n              </div>\n            </div>\n            \n            {/* Progress Indicator */}\n            {isPlaying && (\n              <div className=\"absolute bottom-4 left-4 right-4\">\n                <div className=\"bg-black bg-opacity-50 rounded-full h-1\">\n                  <div \n                    className=\"bg-blue-500 h-1 rounded-full transition-all duration-3000 ease-linear\"\n                    style={{ width: isPlaying ? '100%' : '0%' }}\n                  ></div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Step Details */}\n          <div>\n            <h4 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {steps[currentStep].title}\n            </h4>\n            <p className=\"text-gray-600 mb-6\">\n              {steps[currentStep].description}\n            </p>\n            \n            <div className=\"space-y-3\">\n              <h5 className=\"font-semibold text-gray-900\">Key Features:</h5>\n              {steps[currentStep].features.map((feature, index) => (\n                <div key={index} className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 text-green-500 mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span className=\"text-gray-700\">{feature}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Demo Actions */}\n        <div className=\"mt-8 pt-6 border-t border-gray-200\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"btn-primary\">\n              Try Free Demo\n            </button>\n            <button className=\"btn-outline\">\n              Schedule Live Demo\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAgBA,MAAM,YAAY;IAChB,WAAW;QACT;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;KACD;IACD,SAAS;QACP;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;KACD;IACD,SAAS;QACP;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;KACD;AACH;AAEO,SAAS,YAAY,EAAE,WAAW,EAAoB;;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,QAAQ,SAAS,CAAC,YAAY;IAEpC,MAAM,iBAAiB;QACrB,aAAa;QACb,eAAe;QAEf,6BAA6B;QAC7B,MAAM,WAAW,YAAY;YAC3B,eAAe,CAAC;gBACd,IAAI,QAAQ,MAAM,MAAM,GAAG,GAAG;oBAC5B,aAAa;oBACb,cAAc;oBACd,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;IACL;IAEA,MAAM,kBAAkB,CAAC;QACvB,eAAe;QACf,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;;wCAAgB;wCAAa;wCAAY;;;;;;;;;;;;;sCAExD,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,0BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAA6B;;6DAI9C;;kDACE,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0G,UAAS;;;;;;;;;;;oCAC1I;;;;;;;;;;;;;;;;;;;0BAShB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,yEAAyE,EACnF,UAAU,cACN,8BACA,+CACJ;;oCAED,QAAQ;oCAAE;oCAAG,KAAK,KAAK;;+BARnB,KAAK,EAAE;;;;;;;;;;kCAclB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAE,WAAU;;wDAA6B,KAAK,CAAC,YAAY,CAAC,KAAK;wDAAC;;;;;;;8DACnE,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;oCAK7C,2BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,YAAY,SAAS;gDAAK;;;;;;;;;;;;;;;;;;;;;;0CAQpD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,KAAK,CAAC,YAAY,CAAC,KAAK;;;;;;kDAE3B,6LAAC;wCAAE,WAAU;kDACV,KAAK,CAAC,YAAY,CAAC,WAAW;;;;;;kDAGjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;4CAC3C,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;4DAA4C,MAAK;4DAAe,SAAQ;sEACrF,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;sEAE3J,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAJzB;;;;;;;;;;;;;;;;;;;;;;;kCAYlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAc;;;;;;8CAGhC,6LAAC;oCAAO,WAAU;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;GA/IgB;KAAA", "debugId": null}}]}